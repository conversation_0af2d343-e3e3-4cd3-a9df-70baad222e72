// Screenshot helper for generating Telegram Mini App preview images
export class ScreenshotHelper {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor() {
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d')!;
  }

  // Set canvas size for Telegram preview (1080x1920)
  private setupCanvas() {
    this.canvas.width = 1080;
    this.canvas.height = 1920;
    this.ctx.fillStyle = '#000000';
    this.ctx.fillRect(0, 0, 1080, 1920);
  }

  // Capture current page as screenshot
  public async captureCurrentPage(filename: string = 'tlock-screenshot'): Promise<void> {
    try {
      // Use html2canvas if available, otherwise provide instructions
      if (typeof window !== 'undefined' && (window as any).html2canvas) {
        const html2canvas = (window as any).html2canvas;
        
        const element = document.body;
        const canvas = await html2canvas(element, {
          width: 375,
          height: 812,
          scale: 2.88, // Scale to get 1080x1920
          backgroundColor: '#000000',
          useCORS: true,
          allowTaint: true
        });

        // Download the image
        const link = document.createElement('a');
        link.download = `${filename}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();
      } else {
        this.showScreenshotInstructions();
      }
    } catch (error) {
      this.showScreenshotInstructions();
    }
  }

  // Show manual screenshot instructions
  private showScreenshotInstructions() {
    const instructions = `
📸 手动截图指南

1. 打开浏览器开发者工具 (F12)
2. 点击设备模拟器图标 (📱)
3. 选择设备：iPhone X (375 x 812)
4. 刷新页面确保正确显示
5. 右键点击页面 → "截图" 或使用截图工具
6. 将图片调整为 1080x1920 像素

推荐截图页面：
- /mining (主页)
- /earn (任务页面)  
- /friends (好友页面)
- 聊天弹窗 (点击发送按钮)
    `;
    
    alert(instructions);
  }

  // Generate app icon from current logo
  public generateAppIcon(): void {
    this.canvas.width = 512;
    this.canvas.height = 512;
    
    // Create a simple icon background
    const gradient = this.ctx.createLinearGradient(0, 0, 512, 512);
    gradient.addColorStop(0, '#259AEE');
    gradient.addColorStop(1, '#1A73E8');
    
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, 512, 512);
    
    // Add rounded corners
    this.ctx.globalCompositeOperation = 'destination-in';
    this.ctx.beginPath();
    this.ctx.roundRect(0, 0, 512, 512, 80);
    this.ctx.fill();
    
    // Add Tlock text
    this.ctx.globalCompositeOperation = 'source-over';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = 'bold 72px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('Tlock', 256, 256);
    
    // Download the icon
    const link = document.createElement('a');
    link.download = 'tlock-app-icon-512x512.png';
    link.href = this.canvas.toDataURL('image/png');
    link.click();
  }

  // Create a preview video frame sequence
  public createVideoFrames(): void {
    const instructions = `
🎬 预览视频制作指南

建议使用屏幕录制工具：
- macOS: QuickTime Player (文件 → 新建屏幕录制)
- Windows: Xbox Game Bar (Win + G)
- Chrome: 开发者工具 → 更多工具 → 录制

录制流程 (30秒)：
1. 0-5秒: 显示主页，展示用户信息和TOK余额
2. 5-10秒: 点击Claim按钮，展示挖矿功能
3. 10-15秒: 切换到Earn页面，展示任务列表
4. 15-20秒: 切换到Friends页面，展示邀请功能
5. 20-25秒: 点击发送按钮，展示聊天功能
6. 25-30秒: 回到主页，展示完整界面

录制设置：
- 分辨率: 1080x1920 或 375x812 (后期缩放)
- 帧率: 30fps
- 格式: MP4
- 时长: 最长30秒
    `;
    
    alert(instructions);
    console.log(instructions);
  }
}

// Export singleton instance
export const screenshotHelper = new ScreenshotHelper();

// Add global functions for easy access in console
if (typeof window !== 'undefined') {
  (window as any).captureScreenshot = (filename?: string) => {
    screenshotHelper.captureCurrentPage(filename);
  };
  
  (window as any).generateAppIcon = () => {
    screenshotHelper.generateAppIcon();
  };
  
  (window as any).showVideoGuide = () => {
    screenshotHelper.createVideoFrames();
  };
}
