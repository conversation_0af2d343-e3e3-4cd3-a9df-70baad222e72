# Tlock 生产环境部署指南

## 🎯 生产环境信息

- **域名**: https://tg.tlock.org
- **机器人**: @HabbyBabyBot
- **邀请链接格式**: `https://t.me/HabbyBabyBot?startapp=ref_INVITECODE`

## 🚀 快速部署

### 1. 构建生产版本
```bash
npm run build:prod
```

这个命令会：
- 执行类型检查
- 执行代码检查
- 构建应用到 `dist/` 文件夹
- 自动更新机器人配置为生产域名

### 2. 部署到服务器
将 `dist/` 文件夹的内容上传到你的服务器：

```bash
# 示例：使用 scp 上传
scp -r dist/* <EMAIL>:/var/www/html/

# 或使用 rsync
rsync -av dist/ <EMAIL>:/var/www/html/
```

### 3. 配置服务器
确保服务器配置：
- ✅ HTTPS 证书已配置
- ✅ 域名 `tg.tlock.org` 指向正确的服务器
- ✅ 静态文件服务正常工作
- ✅ 支持 SPA 路由（所有路由都返回 index.html）

## 📋 服务器配置示例

### Nginx 配置
```nginx
server {
    listen 443 ssl http2;
    server_name tg.tlock.org;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    root /var/www/html;
    index index.html;
    
    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
}
```

### Apache 配置
```apache
<VirtualHost *:443>
    ServerName tg.tlock.org
    DocumentRoot /var/www/html
    
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # SPA 路由支持
    <Directory "/var/www/html">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
</VirtualHost>
```

## 🔧 手动配置机器人

如果需要手动更新机器人配置：

```bash
npm run bot:update https://tg.tlock.org
```

## 📱 测试清单

部署完成后，请测试以下功能：

### 基本功能测试
- [ ] 访问 https://tg.tlock.org 正常加载
- [ ] 页面样式和功能正常
- [ ] 路由跳转正常工作

### 机器人功能测试
- [ ] 在 Telegram 中找到 @HabbyBabyBot
- [ ] 发送 `/start` 命令
- [ ] 菜单按钮 "🚀 启动 Tlock" 出现
- [ ] 点击菜单按钮能正常打开应用
- [ ] 登录功能正常工作

### 邀请链接测试
- [ ] 邀请链接格式正确：`https://t.me/HabbyBabyBot?startapp=ref_INVITECODE`
- [ ] 点击邀请链接能正常打开应用
- [ ] 邀请码参数正确传递

### API 功能测试
- [ ] 登录 API 调用正常
- [ ] 挖矿数据获取正常
- [ ] 聊天室消息获取正常
- [ ] 邀请功能正常工作

## 🔄 更新部署

当需要更新应用时：

1. **拉取最新代码**
2. **重新构建**：`npm run build:prod`
3. **上传新文件**到服务器
4. **清除浏览器缓存**测试

## 🚨 故障排除

### 问题 1: 机器人显示 "bot application not found"
- 检查域名 https://tg.tlock.org 是否可访问
- 确认机器人配置已更新
- 等待几分钟让配置生效

### 问题 2: 页面加载失败
- 检查 HTTPS 证书是否正确配置
- 确认服务器防火墙设置
- 检查 Nginx/Apache 配置

### 问题 3: 邀请链接不工作
- 确认邀请码格式包含 `ref_` 前缀
- 检查应用中的邀请链接生成逻辑
- 测试不同的邀请码

### 问题 4: API 调用失败
- 检查 CORS 设置
- 确认 API 服务器 https://api.tlock.xyz 可访问
- 检查网络连接

## 📞 支持

如果遇到部署问题，请检查：
1. 服务器日志
2. 浏览器控制台错误
3. 网络连接状态
4. SSL 证书有效性
