[Unit]
Description=Tlock Telegram Bot Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=your-username
Group=your-group
WorkingDirectory=/path/to/your/tlockdart
ExecStart=/usr/bin/dart run bin/teledart_bot.dart
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=NODE_ENV=production
Environment=TELEGRAM_BOT_TOKEN=**********************************************

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/path/to/your/tlockdart/data
ReadWritePaths=/path/to/your/tlockdart/logs

# 资源限制
LimitNOFILE=65536
MemoryMax=512M

[Install]
WantedBy=multi-user.target
