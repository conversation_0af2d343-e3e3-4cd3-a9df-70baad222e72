/**
 * Telegram Web App Service
 * Handles all interactions with Telegram Web App API
 */

export class TelegramService {
  private static instance: TelegramService;
  private webApp: typeof window.Telegram.WebApp | null = null;

  private constructor() {
    this.initialize();
  }

  public static getInstance(): TelegramService {
    if (!TelegramService.instance) {
      TelegramService.instance = new TelegramService();
    }
    return TelegramService.instance;
  }

  private initialize(): void {
    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      this.webApp = window.Telegram.WebApp;
      this.webApp.ready();
      this.webApp.expand();
      
      // Set up theme change listener
      this.webApp.onEvent('themeChanged', this.handleThemeChange.bind(this));
      this.webApp.onEvent('viewportChanged', this.handleViewportChange.bind(this));
    } else {
      // Telegram Web App not available - running in development mode
    }
  }

  private handleThemeChange(): void {
    if (this.webApp?.themeParams) {
      const root = document.documentElement;
      Object.entries(this.webApp.themeParams).forEach(([key, value]) => {
        if (value) {
          root.style.setProperty(`--tg-theme-${key.replace(/_/g, '-')}`, value);
        }
      });
      root.style.setProperty('--tg-color-scheme', this.webApp.colorScheme);
    }
  }

  private handleViewportChange(): void {
    if (this.webApp) {
      document.documentElement.style.setProperty(
        '--tg-viewport-height', 
        `${this.webApp.viewportHeight}px`
      );
      document.documentElement.style.setProperty(
        '--tg-viewport-stable-height', 
        `${this.webApp.viewportStableHeight}px`
      );
    }
  }

  public isAvailable(): boolean {
    return this.webApp !== null;
  }

  public getUser() {
    return this.webApp?.initDataUnsafe?.user || null;
  }

  public getInitData(): string {
    return this.webApp?.initData || '';
  }

  public getTheme(): 'light' | 'dark' {
    return this.webApp?.colorScheme || 'dark';
  }

  public showMainButton(text: string, onClick: () => void): void {
    if (this.webApp?.MainButton) {
      this.webApp.MainButton.setText(text);
      this.webApp.MainButton.onClick(onClick);
      this.webApp.MainButton.show();
    }
  }

  public hideMainButton(): void {
    if (this.webApp?.MainButton) {
      this.webApp.MainButton.hide();
    }
  }

  public showBackButton(onClick: () => void): void {
    if (this.webApp?.BackButton) {
      this.webApp.BackButton.onClick(onClick);
      this.webApp.BackButton.show();
    }
  }

  public hideBackButton(): void {
    if (this.webApp?.BackButton) {
      this.webApp.BackButton.hide();
    }
  }

  public showAlert(message: string): Promise<void> {
    return new Promise((resolve) => {
      if (this.webApp?.showAlert) {
        this.webApp.showAlert(message, resolve);
      } else {
        alert(message);
        resolve();
      }
    });
  }

  public showConfirm(message: string): Promise<boolean> {
    return new Promise((resolve) => {
      if (this.webApp?.showConfirm) {
        this.webApp.showConfirm(message, resolve);
      } else {
        resolve(confirm(message));
      }
    });
  }

  public openLink(url: string): void {
    if (this.webApp?.openLink) {
      this.webApp.openLink(url);
    } else {
      window.open(url, '_blank');
    }
  }

  public sendData(data: string): void {
    if (this.webApp?.sendData) {
      this.webApp.sendData(data);
    } else {
      // Data would be sent in production
    }
  }

  public close(): void {
    if (this.webApp?.close) {
      this.webApp.close();
    }
  }

  public enableClosingConfirmation(): void {
    if (this.webApp?.enableClosingConfirmation) {
      this.webApp.enableClosingConfirmation();
    }
  }

  public disableClosingConfirmation(): void {
    if (this.webApp?.disableClosingConfirmation) {
      this.webApp.disableClosingConfirmation();
    }
  }
}

// Export singleton instance
export const telegramService = TelegramService.getInstance();
