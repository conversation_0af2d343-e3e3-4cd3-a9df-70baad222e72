import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { UserData, apiService, getTelegramLoginParams } from '../services/api';
import { InviteService } from '../services/inviteService';

interface UserContextType {
  userData: UserData | null;
  isLoading: boolean;
  error: string | null;
  login: () => Promise<void>;
  logout: () => void;
  updateUserData: (data: Partial<UserData>) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

// Custom hook to use user context
function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 登录函数（供手动调用）
  const login = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('UserContext: Manual login process...');
      const loginParams = getTelegramLoginParams();
      console.log('UserContext: Using login params:', loginParams);
      const data = await apiService.login(loginParams);
      console.log('UserContext: Manual login successful:', data);

      // 保存邀请码（如果登录接口返回了邀请码）
      if (data.invitationCode) {
        InviteService.setInvitationCode(data.invitationCode);
        console.log('UserContext: Invitation code saved:', data.invitationCode);
      }

      setUserData(data);
    } catch (err) {
      console.error('UserContext: Manual login failed:', err);
      setError(err instanceof Error ? err.message : 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  // 登出函数
  const logout = () => {
    apiService.clearToken();
    InviteService.clearInvitationCode(); // 清除邀请码
    setUserData(null);
    setError(null);
  };

  // 更新用户数据
  const updateUserData = (data: Partial<UserData>) => {
    if (userData) {
      setUserData({ ...userData, ...data });
    }
  };

  // 应用启动时自动登录
  useEffect(() => {
    const initializeUser = async () => {
      try {
        console.log('UserContext: Initializing user...');
        setIsLoading(true);
        setError(null);

        // 检查是否已有token
        const existingToken = apiService.getToken();

        if (existingToken) {
          console.log('UserContext: Found existing token:', existingToken);
        } else {
          console.log('UserContext: No existing token found');
        }

        // 执行登录
        const loginParams = getTelegramLoginParams();
        console.log('UserContext: Using login params:', loginParams);
        const data = await apiService.login(loginParams);

        // 保存邀请码（如果登录接口返回了邀请码）
        if (data.invitationCode) {
          InviteService.setInvitationCode(data.invitationCode);
          console.log('UserContext: Invitation code saved:', data.invitationCode);
        }

        setUserData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Login failed');
      } finally {
        setIsLoading(false);
      }
    };

    initializeUser();
  }, []); // 空依赖数组，只在组件挂载时执行一次

  const value: UserContextType = {
    userData,
    isLoading,
    error,
    login,
    logout,
    updateUserData
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

// Export the hook
export { useUser };
