import { useState, useEffect } from 'react';
import { Router } from './components/Router';
import { UserProvider } from './contexts/UserContext';
// import { apiService, getTelegramLoginParams } from './services/api';
// import { isTelegramWebApp } from './utils/telegram';

// Telegram types are now defined in src/utils/telegram.ts

// 错误显示组件
function ErrorDisplay({ error, onRetry }: { error: string; onRetry: () => void }) {
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#171717',
      color: 'white',
      padding: '20px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      textAlign: 'center'
    }}>
      <h1 style={{ color: '#ef4444', marginBottom: '20px' }}>应用启动错误</h1>
      <div style={{
        backgroundColor: '#333',
        padding: '20px',
        borderRadius: '10px',
        marginBottom: '20px',
        maxWidth: '90%',
        wordBreak: 'break-word'
      }}>
        <p style={{ marginBottom: '10px' }}>错误详情：</p>
        <pre style={{ fontSize: '12px', textAlign: 'left' }}>{error}</pre>
      </div>
      <button
        onClick={onRetry}
        style={{
          backgroundColor: '#259AEE',
          color: 'white',
          border: 'none',
          padding: '12px 24px',
          borderRadius: '8px',
          fontSize: '16px',
          cursor: 'pointer'
        }}
      >
        重试
      </button>
    </div>
  );
}



function App() {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  const initializeApp = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Initialize Telegram Web App
      if (window.Telegram?.WebApp) {
        const tg = window.Telegram.WebApp;
        tg.ready();
        tg.expand();

        // Get user data
        if (tg.initDataUnsafe?.user) {
          setUser(tg.initDataUnsafe.user);
        }
      }

      // 登录逻辑已移到UserContext中处理，这里只需要初始化Telegram Web App
      setIsLoading(false);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    initializeApp();
  }, []);

  // 如果有错误，显示错误页面
  if (error) {
    return <ErrorDisplay error={error} onRetry={initializeApp} />;
  }

  // 如果正在加载，显示加载页面
  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#171717',
        color: 'white',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ marginBottom: '20px' }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid #333',
            borderTop: '4px solid #259AEE',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}></div>
        </div>
        <p>正在初始化应用...</p>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <UserProvider>
      <Router user={user} />
    </UserProvider>
  );
}

export default App;
