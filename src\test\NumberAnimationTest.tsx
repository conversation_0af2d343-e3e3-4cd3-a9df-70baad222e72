import React, { useState, useEffect } from 'react';
import { SmoothScrollTOKBalance } from '../components/ui/SmoothScrollNumber';
import { FlipClockTOKBalance } from '../components/ui/FlipClockNumber';
import { SimpleFlipTOKBalance } from '../components/ui/SimpleFlipNumber';
import { TOKBalance } from '../components/ui/AnimatedNumber';

const NumberAnimationTest: React.FC = () => {
  const [balance, setBalance] = useState(1234.56);
  const [autoIncrement, setAutoIncrement] = useState(false);

  // 自动递增测试
  useEffect(() => {
    if (!autoIncrement) return;

    const interval = setInterval(() => {
      setBalance(prev => {
        // 模拟实际的挖矿增长，每秒增加一个小数值
        const increment = 0.01; // 每秒增加0.01
        const newValue = prev + increment;
        console.log('Balance update:', { from: prev, to: newValue, increment });
        return newValue;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [autoIncrement]);

  const testValues = [
    1234.56,
    1234.78,
    1235.12,
    1299.99,
    1300.01,
    999.99,
    1000.00,
    0.01,
    0.99,
    123.456789, // 测试精度问题
    0.1 + 0.2,  // 经典的浮点数精度问题
  ];

  return (
    <div style={{ 
      padding: '20px', 
      background: '#1a1a1a', 
      color: 'white', 
      minHeight: '100vh',
      fontFamily: 'HarmonyOS Sans SC, sans-serif'
    }}>
      <h1>数字动画组件测试</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>当前余额: {balance.toFixed(6)}</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginBottom: '10px' }}>
          {testValues.map((value, index) => (
            <button
              key={index}
              onClick={() => setBalance(value)}
              style={{
                background: '#4CAF50',
                color: 'white',
                border: 'none',
                padding: '8px 12px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {value.toString()}
            </button>
          ))}
        </div>
        <button
          onClick={() => setAutoIncrement(!autoIncrement)}
          style={{
            background: autoIncrement ? '#f44336' : '#2196F3',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          {autoIncrement ? '停止自动递增' : '开始自动递增'}
        </button>
        <button
          onClick={() => setBalance(prev => prev + 0.01)}
          style={{
            background: '#FF9800',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          +0.01
        </button>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
        <div style={{ 
          background: '#2a2a2a', 
          padding: '20px', 
          borderRadius: '8px',
          border: '2px solid #4CAF50'
        }}>
          <h3>SmoothScrollTOKBalance (修复后)</h3>
          <div style={{ 
            background: 'black', 
            padding: '15px', 
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <SmoothScrollTOKBalance
              balance={balance}
              style={{
                fontFamily: 'HarmonyOS Sans SC',
                fontSize: '32px',
                color: 'white'
              }}
            />
          </div>
        </div>

        <div style={{ 
          background: '#2a2a2a', 
          padding: '20px', 
          borderRadius: '8px',
          border: '2px solid #2196F3'
        }}>
          <h3>FlipClockTOKBalance</h3>
          <div style={{ 
            background: 'black', 
            padding: '15px', 
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <FlipClockTOKBalance
              balance={balance}
              style={{
                fontFamily: 'HarmonyOS Sans SC',
                fontSize: '32px',
                color: 'white'
              }}
            />
          </div>
        </div>

        <div style={{ 
          background: '#2a2a2a', 
          padding: '20px', 
          borderRadius: '8px',
          border: '2px solid #FF9800'
        }}>
          <h3>SimpleFlipTOKBalance</h3>
          <div style={{ 
            background: 'black', 
            padding: '15px', 
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <SimpleFlipTOKBalance
              balance={balance}
              style={{
                fontFamily: 'HarmonyOS Sans SC',
                fontSize: '32px',
                color: 'white'
              }}
            />
          </div>
        </div>

        <div style={{ 
          background: '#2a2a2a', 
          padding: '20px', 
          borderRadius: '8px',
          border: '2px solid #9C27B0'
        }}>
          <h3>TOKBalance (AnimatedNumber)</h3>
          <div style={{ 
            background: 'black', 
            padding: '15px', 
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <TOKBalance
              balance={balance}
              style={{
                fontFamily: 'HarmonyOS Sans SC',
                fontSize: '32px',
                color: 'white'
              }}
            />
          </div>
        </div>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', background: '#2a2a2a', borderRadius: '8px' }}>
        <h3>测试说明</h3>
        <ul>
          <li>点击数值按钮测试不同的数字变化</li>
          <li>使用"开始自动递增"测试连续的小数变化</li>
          <li>观察动画过程中和动画结束后的数字显示是否正确</li>
          <li>特别注意小数部分的精度问题</li>
        </ul>
      </div>
    </div>
  );
};

export default NumberAnimationTest;
