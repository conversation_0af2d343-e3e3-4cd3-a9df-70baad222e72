import React, { useState, useEffect, useRef } from 'react';

interface SmoothScrollDigitProps {
  digit: string;
  previousDigit: string;
  isAnimating: boolean;
  duration: number;
  fontSize: string;
}

// 单个数字的平滑滚动组件
const SmoothScrollDigit: React.FC<SmoothScrollDigitProps> = ({
  digit,
  previousDigit,
  isAnimating,
  duration,
  fontSize
}) => {
  const [animationProgress, setAnimationProgress] = useState(0);
  const animationRef = useRef<number>();
  const startTimeRef = useRef<number>();

  useEffect(() => {
    if (isAnimating && digit !== previousDigit && /\d/.test(digit) && /\d/.test(previousDigit)) {
      setAnimationProgress(0);
      startTimeRef.current = undefined;

      const animate = (timestamp: number) => {
        if (!startTimeRef.current) {
          startTimeRef.current = timestamp;
        }

        const elapsed = timestamp - startTimeRef.current;
        const progress = Math.min(elapsed / duration, 1);
        
        // 使用缓动函数
        const easeInOutCubic = (t: number) => {
          return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        };
        
        setAnimationProgress(easeInOutCubic(progress));

        if (progress < 1) {
          animationRef.current = requestAnimationFrame(animate);
        }
      };

      animationRef.current = requestAnimationFrame(animate);

      return () => {
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current);
        }
      };
    } else {
      setAnimationProgress(0);
    }
  }, [isAnimating, digit, previousDigit, duration]);

  // 如果不是数字，直接显示
  if (!/\d/.test(digit)) {
    return (
      <span style={{ fontSize, minWidth: '0.5ch', textAlign: 'center', display: 'inline-block' }}>
        {digit}
      </span>
    );
  }

  const currentNum = parseInt(digit) || 0;
  const prevNum = parseInt(previousDigit) || 0;

  // 统一使用向上滚动效果
  const direction = 1; // 始终向上滚动
  let steps = currentNum - prevNum;

  // 处理跨越边界的情况，确保始终向上滚动
  if (steps < 0) {
    steps = steps + 10; // 如果是减少，转换为向上滚动（如3->1变成3->11）
  }
  if (steps > 5) {
    steps = steps - 10; // 如果步数太大，选择更短的路径（如1->9变成1->(-1)，但方向仍然向上）
  }

  // 计算当前显示的数字
  const getCurrentDisplayDigit = () => {
    if (!isAnimating || animationProgress === 0) {
      return previousDigit;
    }
    if (animationProgress === 1) {
      return digit;
    }

    // 在动画过程中，根据进度计算应该显示的数字
    // 始终向上滚动，计算中间经过的数字
    const totalSteps = Math.abs(steps);
    const currentStep = Math.floor(animationProgress * totalSteps);
    let displayNum = (prevNum + currentStep) % 10;

    return displayNum.toString();
  };

  // 计算滚动位置 - 始终向上滚动
  const getScrollTransform = () => {
    if (!isAnimating) return 'translateY(0%)';

    // 始终向上滚动：负值表示向上移动
    const scrollDistance = animationProgress * 100;
    return `translateY(-${scrollDistance}%)`;
  };

  const currentDisplayDigit = getCurrentDisplayDigit();
  const scrollTransform = getScrollTransform();

  return (
    <div 
      className="relative inline-block overflow-hidden"
      style={{
        height: fontSize,
        lineHeight: fontSize,
        fontSize,
        width: '1ch',
        textAlign: 'center'
      }}
    >
      {/* 数字容器 - 包含当前数字和下一个数字 */}
      <div
        className="relative"
        style={{
          transform: scrollTransform,
          transition: 'none' // 使用requestAnimationFrame，不需要CSS transition
        }}
      >
        {/* 当前数字 */}
        <div 
          className="flex items-center justify-center"
          style={{ height: fontSize, lineHeight: fontSize }}
        >
          {isAnimating ? previousDigit : digit}
        </div>
        
        {/* 下一个数字 - 始终从下方滑入 */}
        {isAnimating && (
          <div
            className="flex items-center justify-center absolute inset-x-0"
            style={{
              height: fontSize,
              lineHeight: fontSize,
              top: '100%' // 始终在下方等待
            }}
          >
            {digit}
          </div>
        )}
      </div>
    </div>
  );
};

interface SmoothScrollNumberProps {
  value: number;
  className?: string;
  style?: React.CSSProperties;
  formatNumber?: (num: number) => string;
  duration?: number;
}

// 默认格式化函数
const defaultFormatNumber = (num: number): string => {
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

export const SmoothScrollNumber: React.FC<SmoothScrollNumberProps> = ({
  value,
  className = '',
  style = {},
  formatNumber = defaultFormatNumber,
  duration = 1000 // 1秒完成动画
}) => {
  const [previousValue, setPreviousValue] = useState(value);
  const [isAnimating, setIsAnimating] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const lastValueRef = useRef(value);

  const currentFormatted = formatNumber(value);
  const previousFormatted = formatNumber(previousValue);

  useEffect(() => {
    // 只有当值真正改变时才触发动画
    if (value !== lastValueRef.current && !isAnimating) {
      console.log('SmoothScrollNumber: 开始滚动动画', { 
        from: previousValue, 
        to: value,
        formatted: { from: previousFormatted, to: currentFormatted }
      });
      
      lastValueRef.current = value;
      setIsAnimating(true);
      
      // 清除之前的定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // 动画结束后更新状态
      timeoutRef.current = setTimeout(() => {
        setPreviousValue(value);
        setIsAnimating(false);
        console.log('SmoothScrollNumber: 滚动动画完成', { value });
      }, duration);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, previousValue, isAnimating, duration, previousFormatted, currentFormatted]);

  // 将格式化后的字符串转换为字符数组
  const currentChars = currentFormatted.split('');
  const previousChars = previousFormatted.split('');

  // 确保两个数组长度一致
  const maxLength = Math.max(currentChars.length, previousChars.length);
  while (currentChars.length < maxLength) {
    currentChars.unshift(' ');
  }
  while (previousChars.length < maxLength) {
    previousChars.unshift(' ');
  }

  const fontSize = style.fontSize || '32px';

  return (
    <div 
      className={className} 
      style={{ 
        ...style, 
        display: 'inline-flex', 
        alignItems: 'center'
      }}
    >
      {currentChars.map((char, index) => {
        const prevChar = previousChars[index] || ' ';
        
        return (
          <SmoothScrollDigit
            key={index}
            digit={char}
            previousDigit={prevChar}
            isAnimating={isAnimating}
            duration={duration}
            fontSize={fontSize}
          />
        );
      })}
    </div>
  );
};

// TOK余额专用组件
interface TOKBalanceProps {
  balance: number;
  className?: string;
  style?: React.CSSProperties;
}

export const SmoothScrollTOKBalance: React.FC<TOKBalanceProps> = ({
  balance,
  className = '',
  style = {}
}) => {
  const formatTOKBalance = (num: number): string => {
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  return (
    <SmoothScrollNumber
      value={balance}
      className={className}
      style={style}
      formatNumber={formatTOKBalance}
      duration={1000} // 1秒完成滚动
    />
  );
};
