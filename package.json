{"name": "tlock-miniapp", "version": "1.0.0", "description": "Tlock Telegram Mini App - Decentralized Social Platform", "type": "module", "scripts": {"dev": "vite --host --port 3001", "build": "vite build", "build:check": "tsc && vite build", "build:telegram": "node scripts/build-for-telegram.cjs", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "analyze": "npx vite-bundle-analyzer", "bot:update": "node scripts/update-bot-config.cjs", "bot:test": "node scripts/test-bot-config.cjs", "build:prod": "node scripts/build-production.cjs", "deploy:secure": "node scripts/secure-deploy.cjs", "verify:prod": "node scripts/verify-deployment.cjs", "prepare": "husky install"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.3", "react-router-dom": "^6.20.0", "react-use": "^17.4.0", "socket.io-client": "^4.7.4", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@twa-dev/types": "^7.0.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "husky": "^8.0.3", "jsdom": "^23.0.1", "lint-staged": "^15.1.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.0", "vite-plugin-pwa": "^0.17.4", "vitest": "^1.0.0", "workbox-window": "^7.0.0"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.10.0", "keywords": ["telegram", "mini-app", "react", "typescript", "vite", "social", "decentralized", "tlock"], "author": "Tlock Development Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tlock/tlock-miniapp.git"}, "bugs": {"url": "https://github.com/tlock/tlock-miniapp/issues"}, "homepage": "https://tlock.xyz"}