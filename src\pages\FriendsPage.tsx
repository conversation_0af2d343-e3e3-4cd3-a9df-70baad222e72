import React, { useState, useEffect } from 'react';
import { Routes } from '../types/routes';
import { InviteService } from '../services/inviteService';
import { apiService, FriendData } from '../services/api';
import { useUser } from '../contexts/UserContext';
import { telegramWebApp, isTelegramWebApp } from '../utils/telegram';

// Import images
import friendsInviteBg from '../assets/images/friends/friends-invite-bg.png';
import friendsIcon from '../assets/images/friends/friends-icon.png';

interface FriendsPageProps {
  user?: any;
  onNavigate: (route: Routes) => void;
}

export const FriendsPage: React.FC<FriendsPageProps> = ({ user, onNavigate }) => {
  const [friends, setFriends] = useState<FriendData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { userData } = useUser();

  // 计算在线好友数量和挖矿倍率
  const onlineFriends = friends.filter(friend => friend.online).length;
  const miningRate = onlineFriends * 0.3; // 挖矿倍率 = 在线好友数 × 0.3

  console.log('Friends页面数据:', {
    totalFriends: friends.length,
    onlineFriends,
    miningRate: miningRate.toFixed(1),
    formula: `${onlineFriends} × 0.3 = ${miningRate.toFixed(1)}`
  });

  // 获取好友列表
  useEffect(() => {
    const fetchFriends = async () => {
      try {
        setLoading(true);
        setError(null);
        const friendsData = await apiService.getFriends();
        setFriends(friendsData);
        console.log('好友列表获取成功:', friendsData);
      } catch (err) {
        console.error('获取好友列表失败:', err);
        setError('Failed to load friends list');
        setFriends([]); // 设置为空数组，显示无好友状态
      } finally {
        setLoading(false);
      }
    };

    fetchFriends();
  }, []);

  // 格式化注册时间
  const formatRegisterTime = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // 生成默认头像颜色
  const getAvatarColor = (id: string) => {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
    const index = parseInt(id.slice(-1)) % colors.length;
    return colors[index];
  };

  const handleInviteFriends = async () => {
    // 直接调用Telegram分享功能
    const shareText = `🚀 Join me on Tlock and start mining TLOCK tokens together!

💰 Get bonus tokens when you join with my link
⚡ Start mining immediately
🎁 Earn rewards for both of us

#Tlock #Mining #Crypto`;

    await InviteService.shareInviteLink(shareText);
  };

  const handleCopy = async () => {
    // 使用InviteService生成邀请链接
    const inviteLink = await InviteService.generateInviteLink();

    if (!inviteLink) {
      alert('邀请码未设置，请先完成登录');
      return;
    }

    // Create the complete invite message
    const inviteText = `🚀 Join me on Tlock and start mining TLOCK tokens together!

💰 Get bonus tokens when you join with my link
⚡ Start mining immediately
🎁 Earn rewards for both of us

Join now: ${inviteLink}

#Tlock #Mining #Crypto`;

    navigator.clipboard.writeText(inviteText).then(() => {
      // Show success message (could add toast notification here)
      alert('Invite message copied to clipboard!');
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = inviteText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Invite message copied to clipboard!');
    });
  };

  return (
    <div className="min-h-screen bg-black text-white flex flex-col relative">


      {/* Header with more spacing to match design */}
      <div className="px-4 pt-24 pb-8">
        <h1
          className="text-center font-bold"
          style={{
            fontFamily: 'HarmonyOS Sans SC',
            fontSize: '24px',
            fontWeight: 700,
            lineHeight: '1em',
            color: '#FFFFFF'
          }}
        >
          Invite friends
        </h1>
      </div>

      {/* Online Status Display */}
      <div className="px-4 mb-8">
        <div className="flex items-center justify-center">
          <img src={friendsIcon} alt="Friends" className="w-4 h-4 mr-1" />
          <span
            className="text-center"
            style={{
              fontFamily: 'PingFang SC',
              fontSize: '15px',
              fontWeight: 400,
              lineHeight: '1em',
              color: '#7DCCF3'
            }}
          >
            {onlineFriends > 0 ? `×${miningRate.toFixed(1)} (${onlineFriends} Online)` : '(0 Online)'}
          </span>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 px-4">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-white">Loading friends...</div>
          </div>
        ) : friends.length === 0 ? (
          /* 无好友状态 - 显示邀请图片和消息 */
          <div className="relative mb-16">
            <div
              className="rounded-2xl overflow-hidden relative"
              style={{
                width: '342px',
                height: '180px',
                margin: '0 auto'
              }}
            >
              {/* Background Image */}
              <img
                src={friendsInviteBg}
                alt="Invite Friends"
                className="w-full h-full object-cover"
              />

              {/* Gradient Overlay */}
              <div
                className="absolute inset-0"
                style={{
                  background: 'linear-gradient(180deg, rgba(0, 0, 0, 0) 19.72%, rgba(0, 0, 0, 0.8) 65.38%, rgba(0, 0, 0, 0.9) 100%)'
                }}
              />

              {/* Text Content */}
              <div className="absolute bottom-0 left-0 right-0 p-6">
                <p
                  className="text-center"
                  style={{
                    fontFamily: 'PingFang SC',
                    fontSize: '15px',
                    fontWeight: 400,
                    lineHeight: '1.5em',
                    color: 'rgba(255, 255, 255, 0.8)'
                  }}
                >
                  You don't have any friends yet, go and invite them to claim your reward!
                </p>
              </div>
            </div>
          </div>
        ) : (
          /* 有好友状态 - 显示好友列表 */
          <div className="mb-8">
            {/* List of Your friends 标题 */}
            <div className="mb-4">
              <h2
                style={{
                  fontFamily: 'PingFang SC',
                  fontSize: '14px',
                  fontWeight: 400,
                  lineHeight: '1em',
                  color: 'rgba(255, 255, 255, 0.5)'
                }}
              >
                List of Your friends
              </h2>
            </div>

            {/* 好友列表 */}
            <div className="space-y-2">
              {friends.map((friend) => (
                <div
                  key={friend.id}
                  className="rounded-2xl p-4 flex items-center justify-between"
                  style={{ backgroundColor: '#13171C' }}
                >
                  <div className="flex items-center">
                    {/* 头像 */}
                    <div
                      className="w-10 h-10 rounded-full flex items-center justify-center mr-4"
                      style={{ backgroundColor: getAvatarColor(friend.id) }}
                    >
                      <span className="text-white font-bold text-sm">
                        {friend.username.charAt(0).toUpperCase()}
                      </span>
                    </div>

                    {/* 用户名 */}
                    <div>
                      <p
                        style={{
                          fontFamily: 'PingFang SC',
                          fontSize: '15px',
                          fontWeight: 400,
                          lineHeight: '1em',
                          color: '#FFFFFF'
                        }}
                      >
                        {friend.username}
                      </p>
                    </div>
                  </div>

                  {/* 在线状态 */}
                  {friend.online && (
                    <div className="flex items-center gap-1">
                      <div
                        className="w-1.5 h-1.5 rounded-full"
                        style={{ backgroundColor: '#7DCCF3' }}
                      />
                      <span
                        style={{
                          fontFamily: 'PingFang SC',
                          fontSize: '15px',
                          fontWeight: 400,
                          lineHeight: '1em',
                          color: '#7DCCF3'
                        }}
                      >
                        Online
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Bottom Buttons - Fixed position as per Figma with proper gap above bottom bar */}
      <div
        className="fixed left-0 right-0 px-4 z-10"
        style={{
          bottom: '88px', // 68px (footer height) + 20px gap
          maxWidth: '375px',
          margin: '0 auto'
        }}
      >
        <div className="flex gap-4">
          {/* Invite Friends Button */}
          <button
            onClick={handleInviteFriends}
            className="flex-1 rounded-2xl flex items-center justify-center"
            style={{
              backgroundColor: '#298AFF',
              padding: '16px 32px',
              borderRadius: '17.36px',
              height: '56px'
            }}
          >
            <span
              style={{
                fontFamily: 'PingFang SC',
                fontSize: '16px',
                fontWeight: 400,
                lineHeight: '1em',
                color: '#FFFFFF'
              }}
            >
              Invite friends
            </span>
          </button>

          {/* Copy Button */}
          <button
            onClick={handleCopy}
            className="rounded-2xl flex items-center justify-center"
            style={{
              backgroundColor: '#298AFF',
              padding: '16px 24px',
              borderRadius: '17.36px',
              height: '56px',
              minWidth: '100px'
            }}
          >
            <span
              style={{
                fontFamily: 'PingFang SC',
                fontSize: '16px',
                fontWeight: 400,
                lineHeight: '1em',
                color: '#FFFFFF'
              }}
            >
              Copy
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};
