import React, { useState, useEffect } from 'react';
import { Routes } from '../types/routes';
import { ChatMessage } from '../components/ChatMessage';
import { ChatModal } from '../components/ChatModal';
import { MiningCountdown } from '../components/MiningCountdown';
import { apiService, ChatRoomMessage } from '../services/api';
import { telegramWebApp, isTelegramWebApp } from '../utils/telegram';
import { useUser } from '../contexts/UserContext';
import { useRealTimeBalance, formatMiningRate, formatMinedAmount } from '../hooks/useRealTimeBalance';
import { SmoothScrollTOKBalance } from '../components/ui/SmoothScrollNumber';
import tlockBirdBase from '../assets/figma/tlock_toucan_base.png';
import tlockBirdOverlay from '../assets/figma/tlock_toucan_overlay.png';
import sendButtonIcon from '../assets/figma/send_button_icon.svg';
import rateIcon from '../assets/figma/rate_icon.png';
import userAvatar from '../assets/figma/user_avatar.png';
import telegramIcon from '../assets/icons/telegram-icon.png';

interface MiningPageProps {
  user?: any;
  onNavigate: (route: Routes) => void;
}

interface ChatMessageData {
  id: string;
  username: string;
  level: number;
  levelColor: string;
  message: string;
  timestamp: string;
  avatar?: string;
}

// 根据等级获取颜色的辅助函数
const getLevelColor = (level: number): string => {
  if (level >= 1 && level <= 10) {
    return '#56A1FF'; // 蓝色 - Lv1-10
  } else if (level >= 11 && level <= 20) {
    return '#4ADE80'; // 绿色 - Lv11-20
  } else if (level >= 21 && level <= 30) {
    return '#F59E0B'; // 橙色 - Lv21-30
  } else if (level >= 31 && level <= 40) {
    return '#EF4444'; // 红色 - Lv31-40
  } else if (level >= 41 && level <= 50) {
    return '#8B5CF6'; // 紫色 - Lv41-50
  } else {
    return '#6B7280'; // 默认灰色 - 其他等级
  }
};

export const MiningPage: React.FC<MiningPageProps> = ({ user, onNavigate }) => {
  // 使用UserContext获取真实用户数据
  const { userData, isLoading, error, updateUserData } = useUser();
  const [isMining, setIsMining] = useState(false);
  const [showChatModal, setShowChatModal] = useState(false);
  const [miningStartTime, setMiningStartTime] = useState<number | undefined>();

  // 默认预设消息
  const DEFAULT_PRESET_MESSAGES = [
    "East or west, home is best.",
    "桜がとても美しいです",
    "A true friend is known in the day of adversity.",
    "Il N'y A Pas De Feu Au Lac!",
    "C'est Génial! Excellent! C'est Magique!"
  ];

  // 从localStorage获取保存的预设消息，如果没有则使用默认消息
  const getStoredPresetMessages = (): string[] => {
    try {
      const stored = localStorage.getItem('tlock_preset_messages');
      if (stored) {
        const parsed = JSON.parse(stored);
        // 确保是数组且长度为5
        if (Array.isArray(parsed) && parsed.length <= 5) {
          return parsed;
        }
      }
    } catch (error) {
      console.error('Failed to load preset messages from localStorage:', error);
    }
    return DEFAULT_PRESET_MESSAGES;
  };

  const [recentChatMessages, setRecentChatMessages] = useState<string[]>(getStoredPresetMessages()); // 存储最新的聊天消息用于预设
  const [messages, setMessages] = useState<ChatMessageData[]>([]);
  const [isLoadingMessages, setIsLoadingMessages] = useState(true);

  // 添加消息容器的引用，用于自动滚动
  const messagesContainerRef = React.useRef<HTMLDivElement>(null);

  // 自动滚动到最新消息
  const scrollToLatestMessage = () => {
    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      container.scrollTop = container.scrollHeight;
    }
  };

  // 加载聊天室消息
  const loadChatRoomMessages = async () => {
    try {
      setIsLoadingMessages(true);
      const chatMessages = await apiService.getChatRoomMessages();

      // 转换API消息格式为组件需要的格式
      const formattedMessages: ChatMessageData[] = chatMessages.map((msg, index) => ({
        id: msg.id?.toString() || index.toString(),
        username: msg.username || 'Anonymous',
        level: msg.level || 1,
        levelColor: getLevelColor(msg.level || 1),
        message: msg.message || '',
        timestamp: msg.timestamp ? new Date(msg.timestamp).toLocaleTimeString('en-US', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }) : new Date().toLocaleTimeString('en-US', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }),
        avatar: msg.avatar || `https://i.pravatar.cc/100?img=${(index % 10) + 1}`
      }));

      setMessages(formattedMessages);
      console.log('聊天室消息加载成功:', formattedMessages);

      // 延迟滚动到最新消息
      setTimeout(() => {
        scrollToLatestMessage();
      }, 100);
    } catch (error) {
      console.error('加载聊天室消息失败:', error);
      // 如果API调用失败，使用默认消息
      const defaultMessages: ChatMessageData[] = [
        {
          id: '1',
          username: 'Arlene McCoy',
          level: 9,
          levelColor: getLevelColor(9),
          message: 'East or west, home is best.',
          timestamp: '12:18:29',
          avatar: 'https://i.pravatar.cc/100?img=1'
        },
        {
          id: '2',
          username: 'Robert Fox',
          level: 16,
          levelColor: getLevelColor(16),
          message: '桜がとても美しいです',
          timestamp: '12:19:08',
          avatar: 'https://i.pravatar.cc/100?img=2'
        }
      ];
      setMessages(defaultMessages);
    } finally {
      setIsLoadingMessages(false);
    }
  };

  // 使用实时余额计算Hook
  const { currentBalance, miningRate, totalMined, miningDuration } = useRealTimeBalance({
    userData,
    isMining,
    miningStartTime
  });

  // 组件加载时获取聊天室消息
  useEffect(() => {
    if (userData?.token) {
      loadChatRoomMessages();
    }
  }, [userData?.token]);

  // 使用真实用户数据，如果没有则使用默认值
  const level = userData?.level || 1;
  const username = userData?.username || 'Guest';
  const avatar = userData?.avatar || userAvatar;

  // 显示的余额：始终使用实时计算的余额，确保数字不会回退
  const displayBalance = currentBalance || (userData?.userBalance || 0);

  // 根据online字段和userClaimEndTime自动开始挖矿
  useEffect(() => {
    if (userData && userData.online && !isMining) {
      const currentTime = Math.floor(Date.now() / 1000);
      const remainingTime = userData.userClaimEndTime - currentTime;

      console.log('用户在线状态，检查剩余时间', {
        online: userData.online,
        userClaimEndTime: userData.userClaimEndTime,
        currentTime: currentTime,
        remainingTime: remainingTime,
        lastSettlementTime: userData.lastSettlementTime
      });

      // 如果还有剩余时间，自动开始挖矿倒计时
      if (remainingTime > 0) {
        // 使用lastSettlementTime作为挖矿开始时间
        setMiningStartTime(userData.lastSettlementTime);
        setIsMining(true);
      } else {
        // 如果时间已过期，确保挖矿状态为false
        setIsMining(false);
        setMiningStartTime(undefined);
      }
    }
  }, [userData, isMining]);

  const startMining = async () => {
    try {
      // 添加触觉反馈
      if (isTelegramWebApp()) {
        telegramWebApp.hapticFeedback('impact', 'medium');
      }

      console.log('开始领取首页奖励...');

      // 调用claimHomeReward API
      const claimResult = await apiService.claimHomeReward();
      console.log('首页奖励领取成功:', claimResult);

      // 记录挖矿开始时间（秒级时间戳）
      const startTime = Math.floor(Date.now() / 1000);
      // 计算新的userClaimEndTime（3小时后）
      const newClaimEndTime = startTime + 10800; // 3小时 = 10800秒

      setMiningStartTime(startTime);
      setIsMining(true);

      // 更新用户数据中的userClaimEndTime
      if (updateUserData) {
        updateUserData({
          userClaimEndTime: newClaimEndTime,
          lastSettlementTime: startTime,
          online: true
        });
      }

      console.log('开始挖矿:', {
        startTime,
        newClaimEndTime,
        userRate: userData?.userRate,
        baseSpeed: userData?.baseSpeed,
        miningRate: userData ? (userData.userRate * userData.baseSpeed) / 3600 : 0,
        claimResult
      });
    } catch (error) {
      console.error('领取首页奖励失败:', error);

      // 即使API调用失败，也允许开始挖矿倒计时
      // 这样用户体验不会被中断
      const startTime = Math.floor(Date.now() / 1000);
      const newClaimEndTime = startTime + 10800; // 3小时后

      setMiningStartTime(startTime);
      setIsMining(true);

      // 更新用户数据
      if (updateUserData) {
        updateUserData({
          userClaimEndTime: newClaimEndTime,
          lastSettlementTime: startTime,
          online: true
        });
      }

      // 可以选择显示错误提示
      if (isTelegramWebApp()) {
        telegramWebApp.showAlert('Failed to claim reward, but mining started.');
      }
    }
  };

  const handleMiningComplete = () => {
    setIsMining(false);
    setMiningStartTime(undefined);

    console.log('挖矿完成:', {
      totalMined,
      miningDuration,
      finalBalance: currentBalance
    });

    // TODO: 挖矿完成后应该调用API更新用户余额
    // 这里可以调用 updateUserData 来更新本地状态
  };

  const handleSendMessage = async (message: string) => {
    // 添加触觉反馈
    if (isTelegramWebApp()) {
      telegramWebApp.hapticFeedback('impact', 'light');
    }

    try {
      // 调用发送消息API
      await apiService.sendMessage(message);
      console.log('消息发送成功:', message);

      // 使用真实用户数据
      const newMessage: ChatMessageData = {
        id: Date.now().toString(),
        username: username,
        level: level,
        levelColor: getLevelColor(level), // 根据等级获取正确的颜色
        message,
        timestamp: new Date().toLocaleTimeString('en-US', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }),
        avatar: avatar // 使用用户头像
      };

      // 添加新消息到列表
      setMessages(prev => [...prev, newMessage]);

      // 更新预设消息列表：将新消息添加到开头，去重并保持最新的5条
      setRecentChatMessages(prev => {
        // 先移除重复的消息（如果存在）
        const filteredPrev = prev.filter(msg => msg !== message);
        // 将新消息添加到开头
        const newRecentMessages = [message, ...filteredPrev];
        // 只保留最新的5条不重复消息
        const finalMessages = newRecentMessages.slice(0, 5);

        // 保存到localStorage
        try {
          localStorage.setItem('tlock_preset_messages', JSON.stringify(finalMessages));
        } catch (error) {
          console.error('Failed to save preset messages to localStorage:', error);
        }

        return finalMessages;
      });

      // 延迟滚动到最新消息，确保DOM已更新
      setTimeout(() => {
        scrollToLatestMessage();
      }, 100);
    } catch (error) {
      console.error('发送消息失败:', error);
      // 可以显示错误提示给用户
      if (isTelegramWebApp()) {
        telegramWebApp.showAlert('Failed to send message. Please try again.');
      }
    }
  };



  // 如果用户数据正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white flex flex-col items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-gray-600 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">正在加载用户数据...</p>
        </div>
      </div>
    );
  }

  // 如果加载失败，显示错误信息
  if (error) {
    return (
      <div className="min-h-screen bg-black text-white flex flex-col items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 mb-4">加载失败: {error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white flex flex-col">



      {/* User Info - Exact Figma Design */}
      <div className="flex items-center justify-between px-4 py-3">
        <button
          className="flex items-center space-x-2"
          onClick={() => onNavigate(Routes.PROFILE)}
        >
          {/* User Avatar - 28x28px */}
          <div className="w-7 h-7 rounded-full overflow-hidden">
            <img
              src={avatar}
              alt={username}
              className="w-full h-full object-cover"
            />
          </div>

          {/* User Name and Level - Column Layout */}
          <div className="flex flex-col" style={{ gap: '3px', width: '103px' }}>
            {/* User Name - 12px PingFang SC */}
            <span
              className="text-white font-normal text-left"
              style={{
                fontFamily: 'PingFang SC',
                fontSize: '12px',
                lineHeight: '1em'
              }}
            >
              {username}
            </span>

            {/* Level Badge - Exact Figma Design */}
            <div
              className="flex items-center rounded-lg px-1 py-0.5"
              style={{
                backgroundColor: 'rgba(118, 197, 255, 0.3)',
                borderRadius: '10px',
                gap: '2px',
                width: 'fit-content'
              }}
            >
              {/* Level Icon - 8x8px */}
              <div className="w-2 h-2 relative">
                <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
                  <rect width="8" height="8" fill="#7ED2FC"/>
                  <circle cx="4" cy="4" r="2.58" fill="#8ADCFD"/>
                  <rect width="2" height="2" fill="white" fillOpacity="0.5"/>
                  <rect x="6" width="2" height="2" fill="white" fillOpacity="0.3"/>
                  <rect x="6" y="6" width="2" height="2" fill="white" fillOpacity="0.2"/>
                  <rect y="6" width="2" height="2" fill="white" fillOpacity="0.5"/>
                </svg>
              </div>

              {/* Level Number - 10px Roboto Bold */}
              <span
                className="font-bold"
                style={{
                  fontFamily: 'Roboto',
                  fontSize: '10px',
                  lineHeight: '1em',
                  color: '#DBF0FF'
                }}
              >
                {level}
              </span>
            </div>
          </div>
        </button>

        {/* Settings Button */}
        <button
          onClick={() => onNavigate(Routes.SETTINGS)}
          className="w-8 h-8 rounded-full flex items-center justify-center border"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            borderColor: 'rgba(255, 255, 255, 0.2)'
          }}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="3" stroke="white" strokeWidth="2"/>
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="white" strokeWidth="2"/>
          </svg>
        </button>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col pb-[160px]">
        {/* Bird Logo and Balance - Exact Figma Design */}
        <div className="flex flex-col items-center justify-center mt-8 mb-8">
          {/* Main Container - 208x92 as per Figma */}
          <div className="relative w-52 h-23" style={{ width: '208px', height: '92px' }}>
            {/* Bird Icon - Positioned at top center */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-11 h-7 relative">
              <img
                src={tlockBirdBase}
                alt="Tlock Bird Base"
                className="absolute inset-0 w-full h-full object-cover"
              />
              <img
                src={tlockBirdOverlay}
                alt="Tlock Bird Overlay"
                className="absolute inset-0 w-full h-full object-cover"
              />
            </div>

            {/* Speed Rate Badge - Positioned like Figma (143, 5) */}
            <div
              className="absolute flex items-center px-1.5 py-1 rounded text-xs"
              style={{
                backgroundColor: 'rgba(125, 204, 243, 0.2)',
                top: '5px',
                right: '17px',
                width: '48px',
                height: '19px'
              }}
            >
              <div className="w-3 h-3 mr-1">
                <img
                  src={rateIcon}
                  alt="Rate"
                  className="w-3 h-3"
                />
              </div>
              <span className="text-blue-300 font-normal text-xs" style={{ fontFamily: 'HarmonyOS Sans SC', color: '#7DCCF3' }}>
                ×{userData?.userRate || 0.9}
              </span>
            </div>

            {/* Black Background for Numbers - (0, 28) 208x48 */}
            <div
              className="absolute bg-black rounded-xl flex flex-col items-center justify-center"
              style={{
                top: '28px',
                left: '0px',
                width: '208px',
                height: '48px'
              }}
            >
              {/* Token Balance - Exact Figma Size: 32px font with Smooth Scroll Animation */}
              <SmoothScrollTOKBalance
                balance={displayBalance}
                className="text-white font-bold leading-none"
                style={{
                  fontFamily: 'HarmonyOS Sans SC',
                  fontSize: '32px',
                  lineHeight: '1em'
                }}
              />

              {/* TOK Label - (91.5, 80) 26x12 */}
              <div
                className="text-gray-400 text-xs text-center"
                style={{
                  fontFamily: 'PingFang SC',
                  fontSize: '12px',
                  color: 'rgba(255, 255, 255, 0.5)',
                  marginTop: '4px'
                }}
              >
                TOK
              </div>
            </div>
          </div>
        </div>

        {/* Chat Messages */}
        <div
          ref={messagesContainerRef}
          className="px-4 space-y-1 overflow-y-auto flex-1"
          style={{
            paddingBottom: isMining ? '115px' : '95px', // Space for buttons + status text
            maxHeight: 'calc(100vh - 320px)' // Prevent overflow
          }}
        >
          {isLoadingMessages ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-400 text-sm">Loading messages...</div>
            </div>
          ) : (
            messages.map(message => (
              <ChatMessage
                key={message.id}
                {...message}
              />
            ))
          )}
        </div>
      </div>

      {/* Mining Status Text - Show above buttons when mining */}
      {isMining && (
        <div className="px-4 mb-2">
          <div className="text-center text-gray-400 text-sm">
            <div className="flex items-center justify-center mb-1">
              <span className="mr-1">▲</span>
              <span>In the free mining process...</span>
            </div>
            <div className="flex items-center justify-center space-x-4 text-xs">
              <span>Rate: {formatMiningRate(miningRate)}</span>
              <span>Mined: {formatMinedAmount(totalMined)}</span>
              <span>Duration: {Math.floor(miningDuration / 60)}:{(miningDuration % 60).toString().padStart(2, '0')}</span>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="fixed bottom-[70px] left-0 right-0 px-4 py-1 bg-black">
        <div className="flex items-center">
          {/* Claim Button - Shows countdown when mining */}
          <button
            onClick={isMining ? undefined : startMining}
            disabled={isMining}
            className={`flex-1 font-semibold h-14 rounded-xl mr-3 transition-all text-base flex items-center justify-center ${
              isMining
                ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {isMining ? (
              <MiningCountdown
                isActive={isMining}
                onComplete={handleMiningComplete}
                duration={userData ? Math.max(0, userData.userClaimEndTime - Math.floor(Date.now() / 1000)) : 10800}
                showOnlyTime={true}
              />
            ) : (
              'Claim'
            )}
          </button>

          {/* Send Button */}
          <button
            onClick={() => setShowChatModal(true)}
            className="rounded-xl flex items-center justify-center hover:bg-blue-600 transition-colors h-14"
            style={{
              width: '60px',
              backgroundColor: '#298AFF'
            }}
          >
            {/* Send Icon - Exact Figma Design */}
            <div className="w-5 h-5 flex items-center justify-center">
              <img
                src={sendButtonIcon}
                alt="Send"
                className="w-4 h-3"
                style={{ width: '16.45px', height: '13.34px' }}
              />
            </div>
          </button>
        </div>
      </div>

      {/* Chat Modal */}
      <ChatModal
        isOpen={showChatModal}
        onClose={() => setShowChatModal(false)}
        onSendMessage={handleSendMessage}
        recentMessages={recentChatMessages}
      />
    </div>
  );
};
