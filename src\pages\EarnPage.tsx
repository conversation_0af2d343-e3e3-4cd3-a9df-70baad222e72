import React, { useState, useEffect } from 'react';
import { Routes } from '../types/routes';
import { ENV_CONFIG } from '../config/environment';
import { apiService, StreakStatus, TaskRewardStatus } from '../services/api';

// Import Figma icons
import dailyRewardIcon from '../assets/icons/daily-reward-icon.png';
import readDocumentIcon from '../assets/icons/read-document-icon.png';
import shareFriendsIcon from '../assets/icons/share-friends-icon.png';
import telegramIcon from '../assets/icons/telegram-icon.png';
import twitterIcon from '../assets/icons/twitter-icon.png';
import tokenIcon from '../assets/icons/token-icon.png';
import arrowRightIcon from '../assets/icons/arrow-right-icon.svg';
import closeIcon from '../assets/icons/close-icon.svg';

// Check icon component based on Figma design
const CheckIcon: React.FC<{ className?: string }> = ({ className = "w-8 h-8" }) => (
  <svg
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="16" cy="16" r="15" fill="#298AFF" stroke="#FFFFFF" strokeWidth="1.7"/>
    <path
      d="M9 16L14 21L23 12"
      stroke="#FFFFFF"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Close icon component based on Figma design
const CloseIcon: React.FC<{ className?: string }> = ({ className = "w-7 h-7" }) => (
  <svg
    className={className}
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="14" cy="14" r="14" fill="rgba(255, 255, 255, 0.2)"/>
    <path
      d="M10 10L18 18M18 10L10 18"
      stroke="rgba(255, 255, 255, 0.2)"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

interface EarnPageProps {
  user?: any;
  onNavigate: (route: Routes) => void;
}

// Daily tasks data based on Figma design
const getDailyTasks = (nextReward?: number) => [
  {
    id: 'claim-streak',
    title: 'Claim streak',
    reward: nextReward ? `+${nextReward}` : '+100',
    icon: dailyRewardIcon,
    type: 'claim-streak',
    highlighted: true, // This task has blue border in Figma
  },
  {
    id: 'read-to-earn',
    title: 'Read to earn',
    reward: '+1,000',
    icon: readDocumentIcon,
    type: 'read-to-earn',
  },
  {
    id: 'share-friends',
    title: 'Share with friends',
    reward: '0.3x/friend',
    icon: shareFriendsIcon,
    type: 'share-friends',
  }
];

// Gold coin mission data based on Figma design
const getGoldCoinMissions = (telegramStatus?: TaskRewardStatus, twitterStatus?: TaskRewardStatus) => [
  {
    id: 'telegram',
    title: 'Telegram',
    reward: `+${telegramStatus?.rewardAmount || 500}`, // 从API获取奖励金额
    icon: telegramIcon,
    type: 1, // type=1 for telegram
    claimed: telegramStatus?.isReward || false,
    rewardAmount: telegramStatus?.rewardAmount || 500,
  },
  {
    id: 'twitter',
    title: 'X',
    reward: `+${twitterStatus?.rewardAmount || 500}`, // 从API获取奖励金额
    icon: twitterIcon,
    type: 2, // type=2 for X
    claimed: twitterStatus?.isReward || false,
    rewardAmount: twitterStatus?.rewardAmount || 500,
  }
];

// 签到奖励配置 - 15天的奖励设置
const STREAK_REWARDS_CONFIG = [
  { day: 1, reward: 100 },
  { day: 2, reward: 100 },
  { day: 3, reward: 100 },
  { day: 4, reward: 100 },
  { day: 5, reward: 100 },
  { day: 6, reward: 200 },
  { day: 7, reward: 200 },
  { day: 8, reward: 200 },
  { day: 9, reward: 200 },
  { day: 10, reward: 200 },
  { day: 11, reward: 400 },
  { day: 12, reward: 400 },
  { day: 13, reward: 400 },
  { day: 14, reward: 400 },
  { day: 15, reward: 1400 },
];

// 签到状态类型
interface DailyReward {
  day: number;
  reward: number;
  claimed: boolean;
  available: boolean;
}

export const EarnPage: React.FC<EarnPageProps> = ({ user, onNavigate }) => {
  const [dailyRewards, setDailyRewards] = useState<DailyReward[]>([]);
  const [showClaimStreak, setShowClaimStreak] = useState(false);
  const [showReadToEarnModal, setShowReadToEarnModal] = useState(false);
  const [streakStatus, setStreakStatus] = useState<StreakStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isClaimingReward, setIsClaimingReward] = useState(false);

  // Gold coin mission状态
  const [telegramRewardStatus, setTelegramRewardStatus] = useState<TaskRewardStatus | null>(null);
  const [twitterRewardStatus, setTwitterRewardStatus] = useState<TaskRewardStatus | null>(null);
  const [isClaimingTask, setIsClaimingTask] = useState<{ [key: number]: boolean }>({});

  // 动态生成任务列表，使用API返回的nextReward
  const dailyTasks = getDailyTasks(streakStatus?.nextReward);

  // 动态生成Gold coin missions，使用API返回的状态
  const goldCoinMissions = getGoldCoinMissions(telegramRewardStatus, twitterRewardStatus);

  // 根据签到状态生成每日奖励数据
  const generateDailyRewards = (status: StreakStatus): DailyReward[] => {
    return STREAK_REWARDS_CONFIG.map((config, index) => {
      const dayNumber = index + 1;

      // 如果今天已经签到，那么completedDays包含了今天
      // 如果今天还没签到，那么completedDays是昨天及之前的天数
      if (status.todayClaimed) {
        // 今天已签到：已完成的天数就是completedDays
        const isCompleted = dayNumber <= status.completedDays;
        const isNextDay = dayNumber === status.completedDays + 1;

        return {
          day: dayNumber,
          reward: config.reward,
          claimed: isCompleted,
          available: false // 今天已签到，没有可领取的
        };
      } else {
        // 今天还没签到：下一个可签到的是completedDays + 1
        const isCompleted = dayNumber <= status.completedDays;
        const isAvailableToday = dayNumber === status.completedDays + 1;

        return {
          day: dayNumber,
          reward: config.reward,
          claimed: isCompleted,
          available: isAvailableToday
        };
      }
    });
  };

  // 获取签到状态
  const fetchStreakStatus = async () => {
    try {
      setIsLoading(true);
      const status = await apiService.getStreakStatus();
      setStreakStatus(status);

      // 根据API返回的状态生成每日奖励数据
      const rewards = generateDailyRewards(status);
      setDailyRewards(rewards);

      console.log('签到状态获取成功:', status);
    } catch (error) {
      console.error('获取签到状态失败:', error);
      // 如果API调用失败，使用默认状态
      const defaultStatus: StreakStatus = {
        uid: 'unknown',
        completedDays: 0,
        todayClaimed: false,
        nextReward: 100
      };
      setStreakStatus(defaultStatus);
      const rewards = generateDailyRewards(defaultStatus);
      setDailyRewards(rewards);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取任务奖励状态
  const fetchTaskRewardStatus = async () => {
    try {
      console.log('开始获取任务奖励状态...');

      // 获取Telegram任务状态 (type=1)
      const telegramStatus = await apiService.getTaskRewardStatus(1);
      setTelegramRewardStatus(telegramStatus);
      console.log('Telegram任务状态获取成功:', {
        isReward: telegramStatus.isReward,
        rewardAmount: telegramStatus.rewardAmount
      });

      // 获取X任务状态 (type=2)
      const twitterStatus = await apiService.getTaskRewardStatus(2);
      setTwitterRewardStatus(twitterStatus);
      console.log('X任务状态获取成功:', {
        isReward: twitterStatus.isReward,
        rewardAmount: twitterStatus.rewardAmount
      });

      console.log('任务奖励状态获取完成');
    } catch (error) {
      console.error('获取任务奖励状态失败:', error);
      // 如果API调用失败，使用默认状态
      setTelegramRewardStatus({ isReward: false, rewardAmount: 500 });
      setTwitterRewardStatus({ isReward: false, rewardAmount: 500 });
    }
  };

  // 领取签到奖励
  const handleClaimDaily = async () => {
    if (!streakStatus || isClaimingReward) return;

    try {
      setIsClaimingReward(true);

      // 调用领取签到奖励API
      const result = await apiService.claimStreakReward();
      console.log('签到奖励领取成功:', result);

      // 重新获取签到状态以更新UI
      await fetchStreakStatus();

      // 关闭弹窗
      setShowClaimStreak(false);

    } catch (error) {
      console.error('领取签到奖励失败:', error);
      // 可以显示错误提示
    } finally {
      setIsClaimingReward(false);
    }
  };

  // 组件挂载时获取签到状态和任务奖励状态
  useEffect(() => {
    fetchStreakStatus();
    fetchTaskRewardStatus();
  }, []);

  // 监听任务奖励状态变化
  useEffect(() => {
    console.log('Telegram奖励状态更新:', telegramRewardStatus);
  }, [telegramRewardStatus]);

  useEffect(() => {
    console.log('X奖励状态更新:', twitterRewardStatus);
  }, [twitterRewardStatus]);

  const handleTaskClick = (taskId: string) => {
    if (taskId === 'claim-streak') {
      setShowClaimStreak(true);
    } else if (taskId === 'read-to-earn') {
      setShowReadToEarnModal(true);
    } else if (taskId === 'read-document') {
      onNavigate('documents' as Routes);
    } else if (taskId === 'share-friends') {
      onNavigate('friends' as Routes);
    }
  };

  const handleMissionClick = async (missionId: string, type: number, claimed: boolean) => {
    // 如果已经领取过奖励，则不执行任何操作
    if (claimed) {
      console.log(`任务 ${missionId} 已经领取过奖励`);
      return;
    }

    // 如果正在领取中，防止重复点击
    if (isClaimingTask[type]) {
      return;
    }

    try {
      setIsClaimingTask(prev => ({ ...prev, [type]: true }));

      // 先打开相应的链接
      if (missionId === 'telegram') {
        window.open('https://t.me/tlock_official', '_blank');
      } else if (missionId === 'twitter') {
        window.open('https://twitter.com/tlock_official', '_blank');
      }

      // 调用领取任务奖励API
      const result = await apiService.claimTaskReward(type);
      console.log(`${missionId}任务奖励领取成功:`, result);

      // 重新获取任务状态以更新UI
      console.log(`重新获取任务状态前 - ${missionId} claimed状态:`, type === 1 ? telegramRewardStatus?.isReward : twitterRewardStatus?.isReward);

      // 添加小延迟确保服务器状态已更新
      await new Promise(resolve => setTimeout(resolve, 500));
      await fetchTaskRewardStatus();

      console.log(`重新获取任务状态后 - ${missionId} claimed状态:`, type === 1 ? telegramRewardStatus?.isReward : twitterRewardStatus?.isReward);

    } catch (error) {
      console.error(`领取${missionId}任务奖励失败:`, error);
      // 可以显示错误提示
    } finally {
      setIsClaimingTask(prev => ({ ...prev, [type]: false }));
    }
  };

  return (
    <div className="min-h-screen bg-black text-white flex flex-col">
      {/* Main Content */}
      <div className="flex-1 px-4 pt-6 pb-24">
        {/* Page Title - Removed as per Figma design */}

        {/* Daily Tasks Section */}
        <div className="mb-6">
          <h2 className="text-sm text-gray-400 mb-3 opacity-50">Daily tasks</h2>
          <div className="space-y-3">
            {dailyTasks.map((task) => (
              <div
                key={task.id}
                onClick={() => handleTaskClick(task.id)}
                className={`rounded-2xl p-4 flex items-center space-x-4 cursor-pointer hover:opacity-80 transition-opacity ${
                  task.highlighted ? 'border-2 border-blue-500' : ''
                }`}
                style={{ backgroundColor: '#13171C' }}
              >
                {/* Task Icon */}
                <div className="w-12 h-12 flex items-center justify-center">
                  <img src={task.icon} alt={task.title} className="w-12 h-12 object-contain" />
                </div>
                
                {/* Task Info */}
                <div className="flex-1">
                  <h3 className="font-medium text-white mb-1">{task.title}</h3>
                  <div className="flex items-center space-x-1">
                    <img src={tokenIcon} alt="Token" className="w-4 h-4" />
                    <span className="text-sm font-semibold text-white">{task.reward}</span>
                  </div>
                </div>
                
                {/* Arrow Icon */}
                <img src={arrowRightIcon} alt="Arrow" className="w-5 h-5 opacity-30" />
              </div>
            ))}
          </div>
        </div>

        {/* Gold Coin Mission Section */}
        <div className="mb-6">
          <h2 className="text-sm text-gray-400 mb-3 opacity-50">Gold coin mission</h2>
          <div className="space-y-3">
            {goldCoinMissions.map((mission) => (
              <div
                key={mission.id}
                onClick={() => handleMissionClick(mission.id, mission.type, mission.claimed)}
                className={`rounded-2xl p-4 flex items-center space-x-4 transition-opacity ${
                  mission.claimed ? 'cursor-default' : 'cursor-pointer hover:opacity-80'
                }`}
                style={{ backgroundColor: '#13171C' }}
              >
                {/* Mission Icon */}
                <div className="w-12 h-12 flex items-center justify-center">
                  <img src={mission.icon} alt={mission.title} className="w-12 h-12 object-contain" />
                </div>

                {/* Mission Info */}
                <div className="flex-1">
                  <h3 className="font-medium text-white mb-1">{mission.title}</h3>
                  <div className="flex items-center space-x-1">
                    <img src={tokenIcon} alt="Token" className="w-4 h-4" />
                    <span className="text-sm font-semibold text-white">{mission.reward}</span>
                  </div>
                </div>

                {/* Status or Arrow Icon */}
                {mission.claimed ? (
                  <span className="text-sm text-gray-400">Claimed</span>
                ) : isClaimingTask[mission.type] ? (
                  <span className="text-sm text-blue-400">Claiming...</span>
                ) : (
                  <img src={arrowRightIcon} alt="Arrow" className="w-5 h-5 opacity-30" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Development Test Section - Only show in development */}
        {import.meta.env.DEV && (
          <div className="mb-6">
            <h2 className="text-sm text-gray-400 mb-3 opacity-50">Development Tests</h2>
            <div className="space-y-3">
              <button
                onClick={() => onNavigate(Routes.QUIZ_TEST)}
                className="w-full rounded-2xl p-4 flex items-center justify-between cursor-pointer hover:opacity-80 transition-opacity"
                style={{ backgroundColor: '#13171C' }}
              >
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 flex items-center justify-center bg-purple-600 rounded-xl">
                    <span className="text-xl">🧪</span>
                  </div>
                  <div className="text-left">
                    <h3 className="font-medium text-white">Quiz API Test</h3>
                    <p className="text-sm text-gray-400">Test Quiz endpoints</p>
                  </div>
                </div>
                <span className="text-sm text-gray-400">Test</span>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Claim Streak Modal */}
      {showClaimStreak && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-end justify-center z-50">
          <div className="rounded-t-2xl w-full max-w-md p-6" style={{ backgroundColor: '#13171C' }}>
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">Claim Streak</h2>
              <button
                onClick={() => setShowClaimStreak(false)}
                className="w-7 h-7 flex items-center justify-center"
              >
                <CloseIcon className="w-7 h-7" />
              </button>
            </div>

            {/* Calendar Grid - 4 columns layout based on Figma */}
            <div className="grid grid-cols-4 gap-3 mb-6">
              {dailyRewards.slice(0, 15).map((reward) => (
                <div
                  key={reward.day}
                  className={`aspect-square rounded-2xl flex flex-col items-center justify-center p-2 relative transition-all ${
                    reward.available
                      ? 'text-white'
                      : 'text-white'
                  }`}
                  style={{
                    backgroundColor: reward.available ? '#298AFF' : '#202428',
                    border: reward.available ? '2px solid #298AFF' : 'none'
                  }}
                >
                  <div className="text-xs mb-1" style={{ opacity: 0.5 }}>Day {reward.day}</div>
                  <img src={tokenIcon} alt="Token" className="w-7 h-7 mb-1" />
                  <div className="text-sm font-bold">{reward.reward}</div>
                  
                  {/* Claimed Overlay */}
                  {reward.claimed && (
                    <div className="absolute inset-0 bg-black bg-opacity-60 rounded-2xl flex items-center justify-center">
                      <CheckIcon className="w-8 h-8" />
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Claim Button */}
            {isLoading ? (
              <button
                disabled
                className="w-full py-4 text-gray-400 rounded-2xl font-medium"
                style={{ backgroundColor: '#13171C' }}
              >
                Loading...
              </button>
            ) : streakStatus?.todayClaimed ? (
              <button
                disabled
                className="w-full py-4 text-gray-400 rounded-2xl font-medium"
                style={{ backgroundColor: '#13171C' }}
              >
                Received
              </button>
            ) : dailyRewards.some(reward => reward.available) ? (
              <button
                onClick={handleClaimDaily}
                disabled={isClaimingReward}
                className="w-full py-4 bg-blue-500 text-white rounded-2xl font-medium hover:opacity-80 transition-opacity disabled:opacity-50"
              >
                {isClaimingReward ? 'Claiming...' : 'Claim'}
              </button>
            ) : (
              <button
                disabled
                className="w-full py-4 text-gray-400 rounded-2xl font-medium"
                style={{ backgroundColor: '#13171C' }}
              >
                No reward available
              </button>
            )}
          </div>
        </div>
      )}

      {/* Read to Earn Modal */}
      {showReadToEarnModal && (
        <div className="fixed inset-0 z-50 flex items-end">
          {/* Overlay */}
          <div
            className="absolute inset-0 bg-black bg-opacity-60"
            onClick={() => setShowReadToEarnModal(false)}
          />

          {/* Modal Content */}
          <div className="relative w-full max-w-md mx-auto" style={{ backgroundColor: '#13171C' }}>
            <div className="rounded-t-2xl p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-white">Read to Earn</h2>
                <button
                  onClick={() => setShowReadToEarnModal(false)}
                  className="w-7 h-7 flex items-center justify-center rounded-full bg-white bg-opacity-20"
                >
                  <img src={closeIcon} alt="Close" className="w-4 h-4" />
                </button>
              </div>

              {/* Description */}
              <p className="text-white text-center mb-8 leading-relaxed">
                Skim the doc, ace the quiz, and score some rewards. Easy!
              </p>

              {/* Buttons */}
              <div className="space-y-4">
                <button
                  onClick={() => {
                    setShowReadToEarnModal(false);
                    onNavigate(Routes.DOCUMENTS);
                  }}
                  className="w-full py-6 bg-blue-500 text-white rounded-2xl font-medium hover:opacity-80 transition-opacity"
                >
                  Read Docs
                </button>

                <button
                  onClick={() => {
                    setShowReadToEarnModal(false);
                    onNavigate(Routes.QUIZ);
                  }}
                  className="w-full py-6 bg-blue-500 text-white rounded-2xl font-medium hover:opacity-80 transition-opacity"
                >
                  Start Quiz
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
