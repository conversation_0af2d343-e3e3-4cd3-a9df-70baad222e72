import React, { useState, useEffect } from 'react';
import { getUrlParams, getTelegramLoginParams } from '../services/api';

export const UrlParamsTestPage: React.FC = () => {
  const [urlParams, setUrlParams] = useState<any>(null);
  const [loginParams, setLoginParams] = useState<any>(null);
  const [currentUrl, setCurrentUrl] = useState<string>('');

  useEffect(() => {
    // 获取当前URL
    setCurrentUrl(window.location.href);
    
    // 获取URL参数
    const params = getUrlParams();
    setUrlParams(params);
    
    // 获取最终的登录参数
    const finalParams = getTelegramLoginParams();
    setLoginParams(finalParams);
    
    console.log('当前URL:', window.location.href);
    console.log('URL参数:', params);
    console.log('最终登录参数:', finalParams);
  }, []);

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <h1 className="text-2xl font-bold mb-6 text-center text-blue-400">
        URL 参数测试页面
      </h1>
      
      {/* 当前URL */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-2 text-green-400">当前URL</h2>
        <div className="text-xs break-all">
          {currentUrl}
        </div>
      </div>

      {/* URL参数解析结果 */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-2 text-yellow-400">URL参数解析结果</h2>
        {urlParams ? (
          <pre className="text-xs bg-gray-900 p-3 rounded overflow-x-auto">
            {JSON.stringify(urlParams, null, 2)}
          </pre>
        ) : (
          <div className="text-red-400">未检测到URL参数</div>
        )}
      </div>

      {/* 最终登录参数 */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-2 text-blue-400">最终登录参数</h2>
        <pre className="text-xs bg-gray-900 p-3 rounded overflow-x-auto">
          {JSON.stringify(loginParams, null, 2)}
        </pre>
      </div>

      {/* 参数说明 */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-2 text-purple-400">参数获取优先级</h2>
        <ol className="text-sm space-y-2">
          <li className="flex items-start">
            <span className="text-green-400 mr-2">1.</span>
            <span>Telegram Web App 数据（最高优先级）</span>
          </li>
          <li className="flex items-start">
            <span className="text-yellow-400 mr-2">2.</span>
            <span>URL 参数数据（中等优先级）</span>
          </li>
          <li className="flex items-start">
            <span className="text-red-400 mr-2">3.</span>
            <span>默认测试数据（最低优先级）</span>
          </li>
        </ol>
      </div>

      {/* 测试URL示例 */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-2 text-cyan-400">测试URL示例</h2>
        <div className="text-xs space-y-2">
          <div>
            <strong>完整参数:</strong>
            <div className="bg-gray-900 p-2 rounded mt-1 break-all">
              https://tg.tlock.org?id=123456789&username=testuser&first_name=Test&last_name=User
            </div>
          </div>
          <div>
            <strong>最小参数:</strong>
            <div className="bg-gray-900 p-2 rounded mt-1 break-all">
              https://tg.tlock.org?id=123456789&first_name=Test
            </div>
          </div>
        </div>
      </div>

      {/* 实时URL参数监控 */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-2 text-orange-400">实时URL参数</h2>
        <div className="text-xs space-y-1">
          {new URLSearchParams(window.location.search).entries() ? (
            Array.from(new URLSearchParams(window.location.search).entries()).map(([key, value]) => (
              <div key={key} className="flex">
                <span className="text-blue-300 w-20">{key}:</span>
                <span className="text-white">{value}</span>
              </div>
            ))
          ) : (
            <div className="text-gray-400">无URL参数</div>
          )}
        </div>
      </div>
    </div>
  );
};
