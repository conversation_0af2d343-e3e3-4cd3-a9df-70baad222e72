#!/usr/bin/env node

/**
 * Tlock 机器人配置脚本
 * 用于更新 Telegram Bot 的 Web App 配置
 */

const https = require('https');

const BOT_TOKEN = '8054527269:AAEjKEJ8xQjBHvSfg8dwzJ6wCaGP-DGX7yk';

function makeRequest(method, data = {}, retries = 3) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);

    const options = {
      hostname: 'api.telegram.org',
      port: 443,
      path: `/bot${BOT_TOKEN}/${method}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 30000 // 30秒超时
    };

    const attemptRequest = (attempt) => {
      const req = https.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            const result = JSON.parse(responseData);
            resolve(result);
          } catch (error) {
            if (attempt < retries) {
              console.log(`⚠️ 解析响应失败，重试 ${attempt + 1}/${retries}...`);
              setTimeout(() => attemptRequest(attempt + 1), 2000);
            } else {
              reject(error);
            }
          }
        });
      });

      req.on('error', (error) => {
        if (attempt < retries) {
          console.log(`⚠️ 请求失败 (${error.message})，重试 ${attempt + 1}/${retries}...`);
          setTimeout(() => attemptRequest(attempt + 1), 2000);
        } else {
          reject(error);
        }
      });

      req.on('timeout', () => {
        req.destroy();
        if (attempt < retries) {
          console.log(`⚠️ 请求超时，重试 ${attempt + 1}/${retries}...`);
          setTimeout(() => attemptRequest(attempt + 1), 2000);
        } else {
          reject(new Error('Request timeout'));
        }
      });

      req.write(postData);
      req.end();
    };

    attemptRequest(1);
  });
}

async function updateBotConfig(webAppUrl) {
  console.log('🔧 更新 Tlock 机器人配置\n');
  console.log(`🌐 Web App URL: ${webAppUrl}\n`);
  
  try {
    // 完全移除 Web App 菜单按钮，强制所有访问都经过机器人服务
    // 方法1：设置为 commands 类型
    const result = await makeRequest('setChatMenuButton', {
      menu_button: {
        type: 'commands'
      }
    });

    // 方法2：如果还不行，尝试删除菜单按钮
    if (result.ok) {
      console.log('🔧 尝试完全删除菜单按钮...');
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
      await makeRequest('deleteChatMenuButton', {});
    }

    if (result.ok) {
      console.log('✅ 菜单按钮配置更新成功！');

      // 验证配置（增加延迟）
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
      const menuButton = await makeRequest('getChatMenuButton');
      if (menuButton.ok) {
        console.log(`✅ 验证成功: 菜单按钮类型为 ${menuButton.result.type}`);
        console.log('🔒 所有访问将通过机器人服务进行安全验证');
      }
      
      // 设置机器人命令列表（增加延迟避免API限制）
      console.log('\n🔧 设置机器人命令...');
      await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒

      try {
        const commandsResult = await makeRequest('setMyCommands', {
          commands: [
            { command: 'start', description: '启动 TLock 小程序' },
            { command: 'invites', description: '查看邀请统计' },
            { command: 'help', description: '显示帮助信息' },
            { command: 'status', description: '查看机器人状态' }
          ]
        });

        if (commandsResult.ok) {
          console.log('✅ 机器人命令设置成功');
        } else {
          console.log('⚠️ 机器人命令设置失败，但继续执行:', commandsResult);
        }
      } catch (error) {
        console.log('⚠️ 设置机器人命令时出错，但继续执行:', error.message);
      }

      console.log('\n🎉 安全配置更新完成！');
      console.log('\n📱 测试方式：');
      console.log('1. 手动启动: 在 @HabbyBabyBot 对话中发送 /start');
      console.log('2. 邀请链接: https://t.me/HabbyBabyBot?startapp=ref_INVITECODE');
      console.log('   (将 INVITECODE 替换为实际的邀请码)');
      console.log('\n🔒 安全特性：');
      console.log('- 所有访问都经过机器人服务验证');
      console.log('- 邀请码格式和有效性检查');
      console.log('- 完整的访问日志记录');
      console.log('- 异常访问检测');
      
      return true;
    } else {
      console.log('❌ 菜单按钮更新失败:', result);
      return false;
    }
  } catch (error) {
    console.log('❌ 更新配置时出错:', error.message);
    return false;
  }
}

// 从命令行参数获取 Web App URL
const webAppUrl = process.argv[2];

if (!webAppUrl) {
  console.log('❌ 请提供 Web App URL');
  console.log('用法: node scripts/update-bot-config.cjs <WEB_APP_URL>');
  console.log('示例: node scripts/update-bot-config.cjs https://your-domain.ngrok-free.app');
  process.exit(1);
}

if (!webAppUrl.startsWith('https://')) {
  console.log('❌ Web App URL 必须以 https:// 开头');
  process.exit(1);
}

updateBotConfig(webAppUrl).then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ 脚本执行失败:', error);
  process.exit(1);
});
