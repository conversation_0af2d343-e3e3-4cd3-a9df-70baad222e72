#!/usr/bin/env node

/**
 * 测试机器人配置脚本
 * 用于单独测试 Telegram Bot 配置，不影响部署流程
 */

const https = require('https');

const BOT_TOKEN = '8054527269:AAEjKEJ8xQjBHvSfg8dwzJ6wCaGP-DGX7yk';

function makeRequest(method, data = {}) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: 'api.telegram.org',
      port: 443,
      path: `/bot${BOT_TOKEN}/${method}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 10000 // 10秒超时
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

async function testBotConfig() {
  console.log('🧪 测试机器人配置\n');
  
  try {
    // 1. 测试基本连接
    console.log('1️⃣ 测试基本连接...');
    const me = await makeRequest('getMe');
    if (me.ok) {
      console.log(`✅ 机器人连接成功: @${me.result.username}`);
    } else {
      console.log('❌ 机器人连接失败:', me);
      return false;
    }

    // 2. 检查当前菜单按钮状态
    console.log('\n2️⃣ 检查菜单按钮状态...');
    const menuButton = await makeRequest('getChatMenuButton');
    if (menuButton.ok) {
      console.log(`✅ 当前菜单按钮类型: ${menuButton.result.type}`);
    } else {
      console.log('❌ 获取菜单按钮状态失败:', menuButton);
    }

    // 3. 检查当前命令列表
    console.log('\n3️⃣ 检查命令列表...');
    const commands = await makeRequest('getMyCommands');
    if (commands.ok) {
      console.log('✅ 当前命令列表:');
      commands.result.forEach(cmd => {
        console.log(`   /${cmd.command} - ${cmd.description}`);
      });
    } else {
      console.log('❌ 获取命令列表失败:', commands);
    }

    console.log('\n✅ 机器人配置测试完成');
    return true;

  } catch (error) {
    console.log('❌ 测试过程中出错:', error.message);
    return false;
  }
}

testBotConfig().then(success => {
  console.log(success ? '\n🎉 测试成功' : '\n💥 测试失败');
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ 测试脚本执行失败:', error);
  process.exit(1);
});
