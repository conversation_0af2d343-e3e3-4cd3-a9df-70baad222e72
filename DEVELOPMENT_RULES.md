# Tlock Telegram Mini App - Development Rules

## 🌍 Language Requirements

**MANDATORY: All development must be conducted in English only**

- All code, comments, variable names, function names, class names must be in English
- All commit messages, documentation, and README files must be in English
- All console logs, error messages, and user-facing text must be in English
- All API responses and database fields must use English naming conventions
- No Chinese characters, pinyin, or non-English languages allowed in codebase

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI components (Button, Input, etc.)
│   ├── layout/         # Layout components (Header, Footer, etc.)
│   └── features/       # Feature-specific components
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── services/           # API services and Telegram integration
├── store/              # Zustand store slices
├── utils/              # Utility functions and helpers
├── types/              # TypeScript type definitions
├── styles/             # Global styles and theme configuration
├── assets/             # Images, icons, and static files
└── constants/          # Application constants
```

## 📱 Mobile-First Development

### Responsive Design Rules
- Always design for mobile first (375px width minimum)
- Use Tailwind CSS responsive prefixes: `sm:`, `md:`, `lg:`, `xl:`
- Test on iPhone 13 mini (375x812) as primary target
- Ensure touch targets are minimum 44px x 44px
- Implement smooth animations at 60fps
- Optimize for both portrait and landscape orientations

### Performance Requirements
- Bundle size must be under 1MB gzipped
- First Contentful Paint (FCP) under 1.5 seconds
- Largest Contentful Paint (LCP) under 2.5 seconds
- Cumulative Layout Shift (CLS) under 0.1
- Use lazy loading for images and components
- Implement proper code splitting

## 🔧 Technical Standards

### React Component Guidelines
```typescript
// ✅ Good: Functional component with proper typing
interface UserProfileProps {
  userId: string;
  onUpdate: (user: User) => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({ userId, onUpdate }) => {
  // Component logic here
};

// ❌ Bad: No proper typing, unclear naming
const comp = ({ id, cb }) => {
  // Component logic here
};
```

### State Management with Zustand
```typescript
// ✅ Good: Clear store structure
interface UserStore {
  currentUser: User | null;
  isLoading: boolean;
  setCurrentUser: (user: User) => void;
  clearUser: () => void;
}

export const useUserStore = create<UserStore>((set) => ({
  currentUser: null,
  isLoading: false,
  setCurrentUser: (user) => set({ currentUser: user }),
  clearUser: () => set({ currentUser: null }),
}));
```

### API Integration Rules
- Use React Query for all API calls
- Implement proper error handling with user-friendly messages
- Add loading states for all async operations
- Use TypeScript interfaces for all API responses
- Implement retry logic for failed requests

```typescript
// ✅ Good: Proper API hook with error handling
export const useUserProfile = (userId: string) => {
  return useQuery({
    queryKey: ['user', userId],
    queryFn: () => fetchUserProfile(userId),
    onError: (error) => {
      console.error('Failed to fetch user profile:', error);
      toast.error('Unable to load user profile. Please try again.');
    },
  });
};
```

## 📞 Telegram Integration

### Web App API Usage
```typescript
// ✅ Good: Proper Telegram Web App integration
import { WebApp } from '@twa-dev/types';

declare global {
  interface Window {
    Telegram: {
      WebApp: WebApp;
    };
  }
}

// Initialize Telegram Web App
export const initTelegramWebApp = () => {
  const tg = window.Telegram?.WebApp;
  if (tg) {
    tg.ready();
    tg.expand();
    return tg;
  }
  throw new Error('Telegram Web App not available');
};
```

### Theme Integration
- Always use Telegram's theme colors from `themeParams`
- Support both light and dark themes
- Update theme dynamically when user changes Telegram theme
- Use CSS custom properties for theme colors

## 🎨 Styling Guidelines

### Tailwind CSS Usage
```typescript
// ✅ Good: Semantic class combinations
<button className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
  Claim Reward
</button>

// ❌ Bad: Unclear, non-semantic classes
<button className="bg-blue-500 p-2 text-white">
  Button
</button>
```

### Animation Guidelines
- Use CSS transitions for simple state changes
- Implement smooth 60fps animations
- Add loading skeletons for better perceived performance
- Use `transform` and `opacity` for performant animations

## 🔒 Security & Data Handling

### User Data Protection
- Validate all data received from Telegram Web App
- Never trust client-side data for sensitive operations
- Implement proper authentication with backend
- Use secure storage for sensitive information

### Error Handling
```typescript
// ✅ Good: Comprehensive error handling
try {
  const result = await apiCall();
  return result;
} catch (error) {
  console.error('API call failed:', error);
  
  if (error instanceof NetworkError) {
    toast.error('Network connection failed. Please check your internet.');
  } else if (error instanceof ValidationError) {
    toast.error('Invalid data provided. Please check your input.');
  } else {
    toast.error('An unexpected error occurred. Please try again.');
  }
  
  throw error;
}
```

## 🧪 Testing Requirements

### Unit Testing
- Write tests for all utility functions
- Test custom hooks with React Testing Library
- Mock external dependencies (APIs, Telegram Web App)
- Achieve minimum 80% code coverage

### Integration Testing
- Test critical user flows (login, rewards, social features)
- Test Telegram Web App integration
- Test responsive design on different screen sizes

## 📝 Code Quality

### ESLint Configuration
```json
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "rules": {
    "no-console": "warn",
    "prefer-const": "error",
    "@typescript-eslint/no-unused-vars": "error",
    "react/prop-types": "off"
  }
}
```

### Commit Message Format
```
feat: add user profile component
fix: resolve mining timer synchronization issue
docs: update API integration guide
style: improve button hover animations
refactor: optimize state management structure
test: add unit tests for reward calculation
```

## 🚀 Deployment & Build

### Build Optimization
- Enable tree shaking for unused code elimination
- Use dynamic imports for code splitting
- Optimize images with proper formats (WebP, AVIF)
- Implement service worker for caching
- Minify and compress all assets

### Environment Configuration
- Use environment variables for API endpoints
- Separate development, staging, and production configs
- Never commit sensitive keys or tokens
- Use proper CORS configuration for Telegram domains

## 📊 Monitoring & Analytics

### Performance Monitoring
- Implement Web Vitals tracking
- Monitor bundle size changes
- Track API response times
- Log critical user actions

### Error Tracking
- Implement proper error boundaries
- Use error tracking service (Sentry recommended)
- Log user feedback and crash reports
- Monitor Telegram Web App specific errors

---

**Remember: This is a Telegram Mini App targeting mobile users. Every decision should prioritize performance, user experience, and mobile optimization while maintaining strict English-only development standards.**
