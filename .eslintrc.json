{
  "root": true,
  "env": {
    "browser": true,
    "es2020": true,
    "node": true
  },
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:react/jsx-runtime"
  ],
  "ignorePatterns": [
    "dist",
    "build",
    "node_modules",
    "*.config.js",
    "*.config.ts"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "plugins": [
    "react-refresh",
    "@typescript-eslint",
    "react",
    "react-hooks"
  ],
  "rules": {
    // React specific rules
    "react-refresh/only-export-components": [
      "warn",
      { "allowConstantExport": true }
    ],
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off",
    "react/jsx-uses-react": "off",
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",

    // TypeScript specific rules
    "@typescript-eslint/no-unused-vars": [
      "error",
      { "argsIgnorePattern": "^_" }
    ],
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-non-null-assertion": "warn",
    "@typescript-eslint/prefer-const": "error",

    // General code quality rules
    "no-console": ["warn", { "allow": ["warn", "error"] }],
    "no-debugger": "error",
    "no-alert": "error",
    "prefer-const": "error",
    "no-var": "error",
    "object-shorthand": "error",
    "prefer-template": "error",
    "template-curly-spacing": "error",
    "arrow-spacing": "error",
    "comma-dangle": ["error", "always-multiline"],
    "semi": ["error", "always"],
    "quotes": ["error", "single", { "avoidEscape": true }],
    "jsx-quotes": ["error", "prefer-double"],

    // English-only enforcement rules
    "spellcheck/spell-checker": "off", // We'll use a custom rule for this
    
    // Import/export rules
    "import/order": "off", // We'll configure this separately if needed
    "import/no-unresolved": "off", // TypeScript handles this
    
    // Performance and best practices
    "no-unused-expressions": "error",
    "no-duplicate-imports": "error",
    "no-useless-return": "error",
    "prefer-arrow-callback": "error",
    "no-nested-ternary": "warn",
    "max-depth": ["warn", 4],
    "max-lines-per-function": ["warn", { "max": 100 }],
    "complexity": ["warn", 10],

    // Accessibility rules (important for mobile apps)
    "jsx-a11y/alt-text": "off", // We'll handle this manually
    "jsx-a11y/click-events-have-key-events": "off", // Mobile-first approach
    "jsx-a11y/no-static-element-interactions": "off" // Mobile touch interactions
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "overrides": [
    {
      "files": ["**/*.test.{ts,tsx}", "**/*.spec.{ts,tsx}"],
      "env": {
        "jest": true,
        "vitest-globals/env": true
      },
      "extends": ["plugin:testing-library/react"],
      "rules": {
        "@typescript-eslint/no-explicit-any": "off",
        "no-console": "off"
      }
    },
    {
      "files": ["vite.config.ts", "tailwind.config.js", "postcss.config.js"],
      "rules": {
        "no-console": "off",
        "@typescript-eslint/no-var-requires": "off"
      }
    }
  ]
}
