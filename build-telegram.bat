@echo off
echo 🚀 Building Tlock for Telegram Mini App...
echo.

echo 📁 Cleaning previous build...
if exist dist rmdir /s /q dist

echo 🔧 Setting production environment...
set NODE_ENV=production

echo 🔨 Building the application...
call npm run build

if %errorlevel% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo ⚡ Optimizing for Telegram Mini App...

echo ✅ Build completed successfully!
echo.
echo 📊 Build Summary:
echo    Output Directory: dist/
echo    Environment: production
echo    Telegram Optimized: Yes
echo.
echo 📋 Next Steps:
echo    1. Upload dist/ folder to your HTTPS server
echo    2. Configure your Telegram bot with @BotFather
echo    3. Set the Mini App URL to your domain
echo    4. Test the integration in Telegram
echo.
echo 🤖 Your Bot Information:
echo    Bot Name: HappyBaby
echo    Bot Token: 8054527269:AAEjKEJ8xQjBHvSfg8dwzJ6wCaGP-DGX7yk
echo    Configure at: @BotFather
echo.
echo 🎉 Ready for Telegram Mini App deployment!
pause
