import React, { useState } from 'react';
import { Routes, routeConfigs } from '../types/routes';

// Import pages
import {
  StartupPage,
  MiningPage,
  DocumentsPage,
  EarnPage,
  FriendsPage,
  SettingsPage
} from '../pages';
import { QuizPage } from '../pages/QuizPage';
import UserProfilePage from '../pages/UserProfilePage';
import { TelegramTestPage } from '../pages/TelegramTestPage';
import { UrlParamsTestPage } from '../pages/UrlParamsTestPage';
import { MediaGeneratorPage } from '../pages/MediaGeneratorPage';
import InvitePage from '../pages/InvitePage';
import { BotTestPage } from '../pages/BotTestPage';
import { QuizTestPage } from '../pages/QuizTestPage';
import NumberAnimationTest from '../test/NumberAnimationTest';

// Import layout components
import { StatusBar } from './layout/StatusBar';
import { BottomNavigation } from './layout/BottomNavigation';

interface RouterProps {
  user?: any;
}

export const Router: React.FC<RouterProps> = ({ user }) => {
  // Get initial route - always start from startup/home page on refresh
  const getInitialRoute = (): Routes => {
    // 刷新浏览器时始终回到首页，符合小程序的用户体验
    // 更新URL到根路径
    window.history.replaceState({}, '', '/');
    return Routes.STARTUP;
  };

  const [currentRoute, setCurrentRoute] = useState<Routes>(getInitialRoute());
  const [routeParams] = useState<Record<string, string>>({});

  // Listen for browser navigation (back/forward buttons)
  React.useEffect(() => {
    const handlePopState = () => {
      const newRoute = getInitialRoute();
      setCurrentRoute(newRoute);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // Simple navigation function
  const navigate = (route: Routes) => {
    setCurrentRoute(route);
    // Update URL without page reload
    const config = routeConfigs[route];
    window.history.pushState({}, '', config.path);
  };

  // Handle back navigation
  const goBack = () => {
    // Simple back navigation - go to mining (main page) for now
    navigate(Routes.MINING);
  };

  // Get current route config
  const currentConfig = routeConfigs[currentRoute];

  // Render current page
  const renderCurrentPage = () => {
    switch (currentRoute) {
      case Routes.STARTUP:
        return <StartupPage onComplete={() => navigate(Routes.MINING)} />;

      case Routes.MINING:
        return <MiningPage user={user} onNavigate={navigate} />;

      case Routes.DOCUMENTS:
        return <DocumentsPage user={user} onNavigate={navigate} />;

      case Routes.EARN:
        return <EarnPage user={user} onNavigate={navigate} />;

      case Routes.FRIENDS:
        return <FriendsPage user={user} onNavigate={navigate} />;

      case Routes.QUIZ:
        return <QuizPage user={user} onNavigate={navigate} />;

      case Routes.SETTINGS:
        return <SettingsPage user={user} onNavigate={navigate} />;

      case Routes.PROFILE:
        return <UserProfilePage user={user} onNavigate={navigate} />;

      case Routes.TELEGRAM_TEST:
        return <TelegramTestPage />;

      case Routes.URL_PARAMS_TEST:
        return <UrlParamsTestPage />;

      case Routes.MEDIA_GENERATOR:
        return <MediaGeneratorPage />;

      case Routes.INVITE:
        return <InvitePage user={user} onNavigate={navigate} />;

      case Routes.BOT_TEST:
        return <BotTestPage />;

      case Routes.QUIZ_TEST:
        return <QuizTestPage user={user} onNavigate={navigate} />;

      case Routes.NUMBER_ANIMATION_TEST:
        return <NumberAnimationTest />;

      default:
        return <MiningPage user={user} onNavigate={navigate} />;
    }
  };

  return (
    <div className="relative w-full h-screen bg-black overflow-hidden">


      {/* Status Bar */}
      {currentRoute !== Routes.STARTUP && (
        <StatusBar
          title={currentConfig.title}
          showBackButton={currentConfig.showBackButton}
          onBackClick={currentConfig.showBackButton ? goBack : undefined}
          showDropdownArrow={currentRoute === Routes.SETTINGS}
          showMenuButton={currentRoute === Routes.SETTINGS}
          onMenuClick={() => console.log('Menu clicked')}
        />
      )}

      {/* Main Content */}
      <div className={`${currentRoute !== Routes.STARTUP ? 'pt-[88px]' : ''} ${currentConfig.showBottomNav ? 'pb-[68px]' : ''} h-full overflow-hidden`}>
        {renderCurrentPage()}
      </div>

      {/* Bottom Navigation */}
      {currentConfig.showBottomNav && (
        <BottomNavigation
          currentRoute={currentRoute}
          onNavigate={navigate}
        />
      )}
    </div>
  );
};
