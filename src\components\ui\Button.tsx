import React from 'react';
import { ButtonProps } from '../../types';
import { LoadingSpinner } from './LoadingSpinner';

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  onClick,
  type = 'button',
  className = '',
}) => {
  const baseClasses = [
    'inline-flex',
    'items-center',
    'justify-center',
    'font-semibold',
    'rounded-xl',
    'transition-all',
    'duration-200',
    'touch-target',
    'active:scale-95',
    'disabled:opacity-50',
    'disabled:cursor-not-allowed',
    'disabled:active:scale-100',
  ];

  const variantClasses = {
    primary: [
      'bg-gradient-to-r',
      'from-primary-500',
      'to-primary-600',
      'text-white',
      'shadow-lg',
      'shadow-primary-500/25',
      'hover:shadow-xl',
      'hover:shadow-primary-500/30',
      'active:shadow-md',
    ],
    secondary: [
      'bg-dark-surface',
      'text-dark-text-primary',
      'border',
      'border-dark-border',
      'hover:bg-dark-surface/80',
      'active:bg-dark-surface/60',
    ],
    outline: [
      'bg-transparent',
      'text-primary-500',
      'border-2',
      'border-primary-500',
      'hover:bg-primary-500',
      'hover:text-white',
      'active:bg-primary-600',
    ],
    ghost: [
      'bg-transparent',
      'text-dark-text-primary',
      'hover:bg-dark-surface/50',
      'active:bg-dark-surface/70',
    ],
    danger: [
      'bg-gradient-to-r',
      'from-red-500',
      'to-red-600',
      'text-white',
      'shadow-lg',
      'shadow-red-500/25',
      'hover:shadow-xl',
      'hover:shadow-red-500/30',
      'active:shadow-md',
    ],
  };

  const sizeClasses = {
    sm: ['px-3', 'py-2', 'text-sm', 'min-h-[36px]'],
    md: ['px-4', 'py-3', 'text-base', 'min-h-[44px]'],
    lg: ['px-6', 'py-4', 'text-lg', 'min-h-[52px]'],
  };

  const classes = [
    ...baseClasses,
    ...variantClasses[variant],
    ...sizeClasses[size],
    className,
  ].join(' ');

  const isDisabled = disabled || loading;

  return (
    <button
      type={type}
      className={classes}
      onClick={onClick}
      disabled={isDisabled}
    >
      {loading && (
        <LoadingSpinner 
          size={size === 'sm' ? 16 : size === 'lg' ? 24 : 20} 
          className="mr-2" 
        />
      )}
      {children}
    </button>
  );
};

// Button variants for common use cases
export const PrimaryButton: React.FC<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button {...props} variant="primary" />
);

export const SecondaryButton: React.FC<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button {...props} variant="secondary" />
);

export const OutlineButton: React.FC<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button {...props} variant="outline" />
);

export const GhostButton: React.FC<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button {...props} variant="ghost" />
);

export const DangerButton: React.FC<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button {...props} variant="danger" />
);
