// 国家数据配置
export interface Country {
  code: string;        // 国家简称
  name: string;        // 国家全名
  flag: string;        // 国旗标识符（用于SVG图标）
}

export const COUNTRIES: Country[] = [
  { code: 'US', name: 'United States', flag: 'US' },
  { code: 'CN', name: 'China', flag: 'CN' },
  { code: 'JP', name: 'Japan', flag: 'JP' },
  { code: 'KR', name: 'South Korea', flag: 'KR' },
  { code: 'GB', name: 'United Kingdom', flag: 'GB' },
  { code: 'DE', name: 'Germany', flag: 'DE' },
  { code: 'FR', name: 'France', flag: 'FR' },
  { code: 'IT', name: 'Italy', flag: 'IT' },
  { code: 'ES', name: 'Spain', flag: 'ES' },
  { code: 'RU', name: 'Russia', flag: 'RU' },
  { code: 'CA', name: 'Canada', flag: 'CA' },
  { code: 'AU', name: 'Australia', flag: 'AU' },
  { code: 'BR', name: 'Brazil', flag: 'BR' },
  { code: 'IN', name: 'India', flag: 'IN' },
  { code: 'MX', name: 'Mexico', flag: 'MX' },
  { code: 'AR', name: 'Argentina', flag: 'AR' },
  { code: 'EE', name: 'Estonia', flag: 'EE' },
  { code: 'FI', name: 'Finland', flag: 'FI' },
  { code: 'SE', name: 'Sweden', flag: 'SE' },
  { code: 'NO', name: 'Norway', flag: 'NO' },
  { code: 'DK', name: 'Denmark', flag: 'DK' },
  { code: 'NL', name: 'Netherlands', flag: 'NL' },
  { code: 'BE', name: 'Belgium', flag: 'BE' },
  { code: 'CH', name: 'Switzerland', flag: 'CH' },
  { code: 'AT', name: 'Austria', flag: 'AT' },
  { code: 'PL', name: 'Poland', flag: 'PL' },
  { code: 'CZ', name: 'Czech Republic', flag: 'CZ' },
  { code: 'HU', name: 'Hungary', flag: 'HU' },
  { code: 'GR', name: 'Greece', flag: 'GR' },
  { code: 'PT', name: 'Portugal', flag: 'PT' },
  { code: 'IE', name: 'Ireland', flag: 'IE' },
  { code: 'LV', name: 'Latvia', flag: 'LV' },
  { code: 'LT', name: 'Lithuania', flag: 'LT' },
  { code: 'SK', name: 'Slovakia', flag: 'SK' },
  { code: 'SI', name: 'Slovenia', flag: 'SI' },
  { code: 'HR', name: 'Croatia', flag: 'HR' },
  { code: 'BG', name: 'Bulgaria', flag: 'BG' },
  { code: 'RO', name: 'Romania', flag: 'RO' },
  { code: 'TR', name: 'Turkey', flag: 'TR' },
  { code: 'IL', name: 'Israel', flag: 'IL' },
  { code: 'AE', name: 'United Arab Emirates', flag: 'AE' },
  { code: 'SA', name: 'Saudi Arabia', flag: 'SA' },
  { code: 'EG', name: 'Egypt', flag: 'EG' },
  { code: 'ZA', name: 'South Africa', flag: 'ZA' },
  { code: 'NG', name: 'Nigeria', flag: 'NG' },
  { code: 'KE', name: 'Kenya', flag: 'KE' },
  { code: 'TH', name: 'Thailand', flag: 'TH' },
  { code: 'VN', name: 'Vietnam', flag: 'VN' },
  { code: 'SG', name: 'Singapore', flag: 'SG' },
  { code: 'MY', name: 'Malaysia', flag: 'MY' },
  { code: 'ID', name: 'Indonesia', flag: 'ID' },
  { code: 'PH', name: 'Philippines', flag: 'PH' },
  { code: 'NZ', name: 'New Zealand', flag: 'NZ' },
];

// 根据国家代码查找国家信息
export const findCountryByCode = (code: string): Country | undefined => {
  return COUNTRIES.find(country => country.code === code);
};

// 根据国家名称查找国家信息
export const findCountryByName = (name: string): Country | undefined => {
  return COUNTRIES.find(country => country.name.toLowerCase() === name.toLowerCase());
};

// 获取默认国家（爱沙尼亚）
export const getDefaultCountry = (): Country => {
  return findCountryByCode('EE') || COUNTRIES[0];
};
