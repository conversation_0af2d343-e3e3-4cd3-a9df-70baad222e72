import { apiService } from './api';

export interface InviteStats {
  invite_count: number;
  referral_balance: number;
  level: string;
  reward_rate: number;
}

export interface InvitedFriend {
  id: number;
  username: string;
  first_name: string;
  joined_date: string;
  status: 'active' | 'inactive';
}

export interface InviteReward {
  level: string;
  reward_rate: number;
  invite_count: number;
  next_level_threshold?: number;
}

// Telegram Bot API 响应类型
export interface TelegramBotInfo {
  ok: boolean;
  result: {
    id: number;
    is_bot: boolean;
    first_name: string;
    username: string;
    can_join_groups: boolean;
    can_read_all_group_messages: boolean;
    supports_inline_queries: boolean;
  };
}

export class InviteService {
  private static readonly BOT_TOKEN = '8054527269:AAEjKEJ8xQjBHvSfg8dwzJ6wCaGP-DGX7yk';
  private static botUsername: string | null = null;
  private static invitationCode: string | null = null;

  /**
   * 通过 Telegram Bot API 获取机器人信息
   */
  private static async fetchBotInfo(): Promise<string | null> {
    try {
      const response = await fetch(`https://api.telegram.org/bot${this.BOT_TOKEN}/getMe`);
      const data: TelegramBotInfo = await response.json();

      if (data.ok && data.result.username) {
        this.botUsername = data.result.username;
        console.log('成功获取机器人用户名:', this.botUsername);
        return this.botUsername;
      } else {
        console.error('获取机器人信息失败:', data);
        return null;
      }
    } catch (error) {
      console.error('调用 Telegram Bot API 失败:', error);
      return null;
    }
  }

  /**
   * 获取机器人用户名（优先使用缓存，如果没有则从API获取）
   */
  private static async getBotUsername(): Promise<string> {
    if (this.botUsername) {
      return this.botUsername;
    }

    const username = await this.fetchBotInfo();
    if (username) {
      return username;
    }

    // 如果API调用失败，使用默认值
    console.warn('无法获取机器人用户名，使用默认值');
    return 'HabbyBabyBot';
  }

  /**
   * 设置用户的邀请码（从login接口获取）
   */
  static setInvitationCode(code: string): void {
    this.invitationCode = code;
    // 可选：保存到localStorage以便刷新后恢复
    if (typeof window !== 'undefined') {
      localStorage.setItem('tlock_invitation_code', code);
    }
  }
  
  /**
   * 获取用户的邀请码
   */
  static getInvitationCode(): string | null {
    if (!this.invitationCode && typeof window !== 'undefined') {
      // 尝试从localStorage恢复
      this.invitationCode = localStorage.getItem('tlock_invitation_code');
    }
    return this.invitationCode;
  }

  /**
   * 清除用户的邀请码
   */
  static clearInvitationCode(): void {
    this.invitationCode = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('tlock_invitation_code');
    }
  }
  
  /**
   * 生成邀请链接
   */
  static async generateInviteLink(): Promise<string | null> {
    const code = this.getInvitationCode();
    if (!code) {
      console.warn('邀请码未设置，请先调用login接口');
      return null;
    }

    const botUsername = await this.getBotUsername();
    // 使用正确的邀请链接格式，邀请码前加 ref_ 前缀
    return `https://t.me/${botUsername}?startapp=ref_${code}`;
  }
  
  /**
   * 获取用户的邀请统计
   */
  static async getInviteStats(userId: number): Promise<InviteStats | null> {
    try {
      const response = await apiService.get(`/invite/stats?user_id=${userId}`);
      return response.data;
    } catch (error) {
      console.error('获取邀请统计时出错:', error);
      return null;
    }
  }
  
  /**
   * 获取用户邀请的好友列表
   */
  static async getInvitedFriends(userId: number): Promise<InvitedFriend[]> {
    try {
      const response = await apiService.get(`/invite/friends?user_id=${userId}`);
      return response.data.friends || [];
    } catch (error) {
      console.error('获取邀请好友列表时出错:', error);
      return [];
    }
  }
  
  /**
   * 计算邀请奖励等级
   */
  static calculateInviteRewards(inviteCount: number): InviteReward {
    // 模仿 PepeCase 的三级奖励系统
    if (inviteCount >= 20) {
      return {
        level: '3',
        reward_rate: 0.025, // 2.5%
        invite_count: inviteCount,
      };
    } else if (inviteCount >= 5) {
      return {
        level: '2',
        reward_rate: 0.05, // 5%
        invite_count: inviteCount,
        next_level_threshold: 20,
      };
    } else if (inviteCount >= 1) {
      return {
        level: '1',
        reward_rate: 0.10, // 10%
        invite_count: inviteCount,
        next_level_threshold: 5,
      };
    } else {
      return {
        level: '0',
        reward_rate: 0,
        invite_count: inviteCount,
        next_level_threshold: 1,
      };
    }
  }
  
  /**
   * 分享邀请链接
   */
  static async shareInviteLink(text?: string): Promise<void> {
    const inviteLink = await this.generateInviteLink();
    if (!inviteLink) {
      console.error('无法生成邀请链接，邀请码未设置');
      return;
    }

    const shareText = text || 'Join TLock and unlock exclusive features! 🔐✨';
    const fullMessage = `${shareText}\n${inviteLink}`;

    // 使用 Telegram Web App API 分享
    if (window.Telegram?.WebApp) {
      try {
        // 直接使用 openTelegramLink 打开分享链接，避免 switchInlineQuery 的格式问题
        window.Telegram.WebApp.openTelegramLink(
          `https://t.me/share/url?url=${encodeURIComponent(inviteLink)}&text=${encodeURIComponent(shareText)}`
        );
      } catch (error) {
        console.error('分享失败:', error);
        // 降级方案：复制到剪贴板
        this.copyToClipboard(fullMessage);
      }
    } else {
      // 如果不在 Telegram 环境中，复制到剪贴板
      this.copyToClipboard(fullMessage);
    }
  }
  
  /**
   * 复制邀请链接到剪贴板
   */
  static async copyToClipboard(text: string): Promise<boolean> {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        return true;
      } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        const result = document.execCommand('copy');
        textArea.remove();
        return result;
      }
    } catch (error) {
      console.error('复制到剪贴板失败:', error);
      return false;
    }
  }
  
  /**
   * 从 URL 参数中解析邀请码
   */
  static parseInviteCodeFromUrl(): string | null {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('invitationCode');
  }
  
  /**
   * 获取邀请奖励描述
   */
  static getRewardDescription(level: string): string {
    switch (level) {
      case '1':
        return '邀请1-4位好友，获得10%奖励';
      case '2':
        return '邀请5-19位好友，获得5%奖励';
      case '3':
        return '邀请20位以上好友，获得2.5%奖励';
      default:
        return '开始邀请好友获得奖励';
    }
  }
  
  /**
   * 格式化余额显示
   */
  static formatBalance(balance: number): string {
    if (balance >= 1000000) {
      return `${(balance / 1000000).toFixed(2)}M`;
    } else if (balance >= 1000) {
      return `${(balance / 1000).toFixed(2)}K`;
    } else {
      return balance.toFixed(2);
    }
  }
}
