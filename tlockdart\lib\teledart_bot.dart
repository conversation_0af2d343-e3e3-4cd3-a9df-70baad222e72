import 'package:teledart/teledart.dart';
import 'package:teledart/telegram.dart';
import 'package:teledart/model.dart';
import 'dart:io';

// 🔒 简单的访问频率控制（生产环境应使用数据库）
Map<int, List<DateTime>> userAccessHistory = {};

// 🔒 简化的邀请统计（仅内存存储，重启后清空）
class InviteStats {
  static Map<String, List<Map<String, dynamic>>> _inviteHistory = {};

  // 记录邀请关系（仅内存）
  static void recordInvitation(Map<String, dynamic> inviteRecord) {
    final inviterCode = inviteRecord['inviter_code']?.toString() ?? 'unknown';

    _inviteHistory[inviterCode] = _inviteHistory[inviterCode] ?? [];
    _inviteHistory[inviterCode]!.add(inviteRecord);

    // 限制内存使用，每个邀请码最多保留100条记录
    if (_inviteHistory[inviterCode]!.length > 100) {
      _inviteHistory[inviterCode] = _inviteHistory[inviterCode]!.sublist(
        _inviteHistory[inviterCode]!.length - 100
      );
    }

    print('📊 内存中记录邀请关系: $inviterCode -> ${inviteRecord['invitee_username']}');
  }

  // 获取邀请统计（仅内存）
  static List<Map<String, dynamic>> getUserInvitations(String inviterCode) {
    return _inviteHistory[inviterCode] ?? [];
  }

  // 获取总体统计
  static Map<String, int> getOverallStats() {
    final stats = <String, int>{};
    _inviteHistory.forEach((code, invites) {
      stats[code] = invites.length;
    });
    return stats;
  }
}

// 🔒 检查用户访问频率
bool checkAccessRate(int userId) {
  final now = DateTime.now();
  final userHistory = userAccessHistory[userId] ?? [];

  // 清理5分钟前的记录
  userHistory.removeWhere((time) => now.difference(time).inMinutes > 5);

  // 检查5分钟内的访问次数
  if (userHistory.length >= 10) {
    print('⚠️ 用户 $userId 访问频率过高，5分钟内已访问 ${userHistory.length} 次');
    return false;
  }

  // 记录本次访问
  userHistory.add(now);
  userAccessHistory[userId] = userHistory;

  return true;
}

Future<void> mainBot() async {
  // 从环境变量获取 Bot Token，提高安全性
  final token = Platform.environment['TELEGRAM_BOT_TOKEN'] ??
      '**********************************************'; // 默认值仅用于开发

  // 从环境变量获取小程序 URL
  final webAppUrl = Platform.environment['WEBAPP_URL'] ?? 'https://tg.tlock.org';

  try {
    final username = (await Telegram(token).getMe()).username;
    print('Bot 启动成功: @$username');

    final teledart = TeleDart(token, Event(username!));
    teledart.start();
    print('TeleDart 服务已启动');

    // /start command with image + welcome message + inline buttons
    teledart.onCommand('start').listen((message) async {
      try {
        final chatId = message.chat.id;
        final user = message.from;

        if (user == null) {
          await teledart.sendMessage(chatId, '❌ 无法获取用户信息，请重试。');
          return;
        }

        // 🔒 检查访问频率
        if (!checkAccessRate(user.id)) {
          await teledart.sendMessage(chatId, '⚠️ 访问过于频繁，请稍后再试。');
          return;
        }

        // 解析启动参数，检查是否包含邀请码
        String? invitationCode;
        final messageText = message.text ?? '';
        if (messageText.contains(' ')) {
          final parts = messageText.split(' ');
          if (parts.length > 1) {
            invitationCode = parts[1];
            print('检测到邀请码: $invitationCode');

            // 🔒 安全验证：检查邀请码格式
            if (!invitationCode.startsWith('ref_')) {
              print('⚠️ 无效的邀请码格式: $invitationCode');
              await teledart.sendMessage(chatId, '❌ 邀请码格式无效，请使用有效的邀请链接。');
              return;
            }

            // 🔒 安全验证：检查邀请码长度
            if (invitationCode.length < 10 || invitationCode.length > 50) {
              print('⚠️ 邀请码长度异常: $invitationCode');
              await teledart.sendMessage(chatId, '❌ 邀请码无效，请使用有效的邀请链接。');
              return;
            }
          }
        }

        final userData = {
          'id': user.id,
          'username': user.username ?? '',
          'first_name': user.first_name,
          'last_name': user.last_name ?? '',
          'language_code': user.language_code ?? 'en',
          'invitation_code': invitationCode ?? '',
        };

        print('用户启动机器人: $userData');

        // 🔒 安全日志：记录访问信息
        final timestamp = DateTime.now().toIso8601String();
        final accessLog = {
          'timestamp': timestamp,
          'user_id': user.id,
          'username': user.username ?? 'unknown',
          'chat_id': chatId,
          'invitation_code': invitationCode ?? 'none',
          'user_agent': 'telegram_bot',
        };
        print('🔒 访问日志: $accessLog');

        // 发送欢迎图片
        try {
          await teledart.sendPhoto(
            chatId,
            'https://download.tlock.xyz/images/photo20250620102720.jpg',
          );
        } catch (e) {
          print('发送图片失败: $e');
          // 图片发送失败不影响主要功能，继续执行
        }

        // 构造小程序 URL，包含用户信息和邀请码
        var appUrl = '$webAppUrl?id=${user.id}&username=${Uri.encodeComponent(user.username ?? '')}&first_name=${Uri.encodeComponent(user.first_name)}&last_name=${Uri.encodeComponent(user.last_name ?? '')}';

        // 如果有邀请码，添加到 URL 中
        if (invitationCode != null && invitationCode.isNotEmpty) {
          appUrl += '&invitationCode=${Uri.encodeComponent(invitationCode)}';
          print('✅ 用户通过邀请码进入: $invitationCode');

          // 🔗 记录邀请关系（仅日志和内存）
          final inviteRecord = {
            'inviter_code': invitationCode,
            'invitee_id': user.id,
            'invitee_username': user.username ?? 'unknown',
            'invitee_first_name': user.first_name,
            'invitee_last_name': user.last_name ?? '',
            'timestamp': DateTime.now().toIso8601String(),
            'chat_id': chatId,
          };
          print('🔗 邀请关系记录: $inviteRecord');

          // 记录到内存（轻量级统计）
          InviteStats.recordInvitation(inviteRecord);
        } else {
          print('ℹ️ 用户直接启动（无邀请码）');
        }

        // 🔒 添加安全时间戳和验证参数
        final urlTimestamp = DateTime.now().millisecondsSinceEpoch;
        appUrl += '&timestamp=$urlTimestamp&verified=true';

        print('生成的小程序 URL: $appUrl');

        // 构建个性化欢迎消息
        String welcomeMessage = "🎉 *TLock is back!* \n\n";
        welcomeMessage += "But we're back bigger and better than before. \n\n";
        welcomeMessage += "Welcome to TLock: a new world with its own blockchain, games, apps, and rewards 🚀\n\n";

        if (invitationCode != null && invitationCode.isNotEmpty) {
          welcomeMessage += "🎁 *您通过邀请链接进入，将获得额外奖励！*\n\n";
        }

        welcomeMessage += "🔒 *安全提示：*\n";
        welcomeMessage += "• 您的访问已通过官方验证\n";
        welcomeMessage += "• 请勿分享您的个人信息给他人\n";
        welcomeMessage += "• 如遇问题请联系官方客服\n\n";
        welcomeMessage += "点击下方按钮安全启动 TLock：";

        // 发送欢迎消息和小程序按钮
        await teledart.sendMessage(
          chatId,
          welcomeMessage,
          parse_mode: 'Markdown',
          reply_markup: InlineKeyboardMarkup(inline_keyboard: [
            [
              InlineKeyboardButton(
                text: "🔒 安全启动 Tlock",
                web_app: WebAppInfo(url: appUrl),
              ),
            ],
            [
              InlineKeyboardButton(
                text: "📢 关注官方频道",
                url: "https://t.me/tlock_official",
              ),
              InlineKeyboardButton(
                text: "🛡️ 安全帮助",
                callback_data: "security_help",
              ),
            ],
          ]),
        );

        print('欢迎消息发送成功，用户ID: ${user.id}');

      } catch (e) {
        print('处理 /start 命令时发生错误: $e');
        try {
          await teledart.sendMessage(
            message.chat.id,
            '❌ 服务暂时不可用，请稍后重试。',
          );
        } catch (sendError) {
          print('发送错误消息失败: $sendError');
        }
      }
    });

    // 帮助命令
    teledart.onCommand('help').listen((message) async {
      try {
        await message.reply(
          '🤖 *TLock Bot 帮助*\n\n'
          '/start - 启动 TLock 小程序\n'
          '/invites - 查看邀请统计（当前会话）\n'
          '/help - 显示此帮助信息\n'
          '/status - 查看机器人状态\n\n'
          '🔒 *安全特性:*\n'
          '• 所有访问都经过安全验证\n'
          '• 邀请关系日志记录\n'
          '• 访问频率保护\n\n'
          '有问题请联系客服 @tlock_support',
          parse_mode: 'Markdown',
        );
      } catch (e) {
        print('发送帮助信息失败: $e');
      }
    });

    // 状态命令
    teledart.onCommand('status').listen((message) async {
      try {
        await message.reply(
          '✅ TLock Bot 运行正常\n'
          '🌐 小程序地址: $webAppUrl\n'
          '⏰ 运行时间: ${DateTime.now().toIso8601String()}',
        );
      } catch (e) {
        print('发送状态信息失败: $e');
      }
    });

    // 邀请统计命令（简化版）
    teledart.onCommand('invites').listen((message) async {
      try {
        final user = message.from;
        if (user == null) return;

        // 获取总体统计
        final overallStats = InviteStats.getOverallStats();
        final totalInvites = overallStats.values.fold(0, (sum, count) => sum + count);

        String statsMessage = '📊 *邀请统计概览*\n\n';
        statsMessage += '🌟 当前会话总邀请数: $totalInvites\n';
        statsMessage += '📈 活跃邀请码数量: ${overallStats.length}\n\n';

        if (overallStats.isNotEmpty) {
          statsMessage += '🏆 *热门邀请码:*\n';
          final sortedStats = overallStats.entries.toList()
            ..sort((a, b) => b.value.compareTo(a.value));

          for (final entry in sortedStats.take(5)) {
            statsMessage += '• ${entry.key}: ${entry.value} 人\n';
          }
        }

        statsMessage += '\n💡 使用 /start 获取您的邀请链接\n';
        statsMessage += '⚠️ 统计数据仅保存在当前会话中';

        await message.reply(statsMessage, parse_mode: 'Markdown');

      } catch (e) {
        print('发送邀请统计失败: $e');
        await message.reply('❌ 获取邀请统计失败，请稍后重试。');
      }
    });

    // 监听所有消息（用于调试和处理特殊情况）
    teledart.onMessage().listen((message) async {
      try {
        final user = message.from;
        final text = message.text ?? '';

        print('收到消息: "$text" 来自用户: ${user?.id} (${user?.first_name})');

        // 如果是未处理的命令，发送帮助信息
        if (text.startsWith('/') &&
            !['start', 'help', 'status'].contains(text.substring(1).split(' ')[0])) {
          await message.reply('❓ 未知命令。输入 /help 查看可用命令。');
        }

        // 如果用户发送了非命令消息，可能是通过邀请链接进入但没有触发 /start
        if (!text.startsWith('/') && text.isNotEmpty) {
          print('用户发送非命令消息，可能需要引导启动小程序');

          // 构造小程序 URL
          var appUrl = '$webAppUrl?id=${user?.id}&username=${Uri.encodeComponent(user?.username ?? '')}&first_name=${Uri.encodeComponent(user?.first_name ?? '')}&last_name=${Uri.encodeComponent(user?.last_name ?? '')}';

          await teledart.sendMessage(
            message.chat.id,
            "👋 欢迎使用 TLock！点击下方按钮启动小程序：",
            reply_markup: InlineKeyboardMarkup(inline_keyboard: [
              [
                InlineKeyboardButton(
                  text: "🎭 启动 Tlock 🎭",
                  web_app: WebAppInfo(url: appUrl),
                ),
              ],
            ]),
          );
        }
      } catch (e) {
        print('处理消息时出错: $e');
      }
    });

    // 处理 Web App 查询（当用户通过邀请链接直接启动 Web App 时）
    teledart.onInlineQuery().listen((query) async {
      try {
        print('收到 Inline Query: ${query.query} 来自用户: ${query.from.id}');

        // 这里可以处理内联查询，比如分享功能
        await teledart.answerInlineQuery(
          query.id,
          [],
          cache_time: 0,
        );
      } catch (e) {
        print('处理 Inline Query 失败: $e');
      }
    });

    // 处理回调查询（按钮点击等）
    teledart.onCallbackQuery().listen((query) async {
      try {
        print('收到 Callback Query: ${query.data} 来自用户: ${query.from.id}');

        if (query.data == 'security_help') {
          // 发送安全帮助信息
          await teledart.sendMessage(
            query.message!.chat.id,
            "🛡️ *TLock 安全指南*\n\n"
            "✅ *安全特性：*\n"
            "• 所有访问都经过官方机器人验证\n"
            "• 邀请码格式和有效性检查\n"
            "• 访问频率限制保护\n"
            "• 完整的操作日志记录\n\n"
            "⚠️ *安全提醒：*\n"
            "• 只通过官方邀请链接进入\n"
            "• 不要向他人透露您的个人信息\n"
            "• 发现异常情况请立即联系客服\n"
            "• 定期检查您的账户安全\n\n"
            "📞 *联系客服：* @tlock_support",
            parse_mode: 'Markdown',
          );
        }

        await teledart.answerCallbackQuery(query.id);
      } catch (e) {
        print('处理 Callback Query 失败: $e');
      }
    });

    print('所有事件处理器已注册完成');

  } catch (e) {
    print('❌ Bot 启动失败: $e');
    print('请检查网络连接和 Bot Token 是否正确');
    exit(1);
  }
}
