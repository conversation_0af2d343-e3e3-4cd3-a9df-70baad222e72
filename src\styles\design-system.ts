// Tlock Design System - Based on Figma Specifications

export const colors = {
  // Primary Colors
  background: '#000000',
  cardBackground: '#13171C',
  border: '#1A1A1A',
  
  // Text Colors
  textPrimary: '#FFFFFF',
  textSecondary: 'rgba(255, 255, 255, 0.5)',
  textTertiary: 'rgba(255, 255, 255, 0.3)',
  
  // Brand Colors
  primary: '#298AFF',
  success: '#00FF88',
  warning: '#FFD700',
  error: '#FF4444',
  
  // Status Colors
  online: '#00FF88',
  offline: '#4E4F53',
  
  // Level Colors
  level1_10: '#6C3306',
  level11_20: '#5C4A5A',
  level21_30: '#EA6A20',
  level31_40: '#BE3A8A',
  
  // Gradient Colors
  gradientStart: '#298AFF',
  gradientEnd: '#00FF88',
} as const;

export const typography = {
  // Font Families
  primary: 'HarmonyOS Sans SC',
  secondary: 'PingFang SC',
  mono: 'Roboto',
  
  // Font Sizes
  xs: '10px',
  sm: '12px',
  base: '14px',
  lg: '16px',
  xl: '18px',
  '2xl': '20px',
  '3xl': '24px',
  '4xl': '32px',
  '5xl': '42px',
  
  // Font Weights
  normalWeight: 400,
  medium: 500,
  semibold: 600,
  bold: 700,

  // Line Heights
  tight: '1em',
  normal: '1.2em',
  relaxed: '1.5em',
} as const;

export const spacing = {
  0: '0px',
  1: '4px',
  2: '8px',
  3: '12px',
  4: '16px',
  5: '20px',
  6: '24px',
  8: '32px',
  10: '40px',
  12: '48px',
  16: '64px',
  20: '80px',
  24: '96px',
} as const;

export const borderRadius = {
  none: '0px',
  sm: '4px',
  base: '8px',
  md: '12px',
  lg: '16px',
  xl: '20px',
  '2xl': '24px',
  full: '9999px',
} as const;

export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
} as const;

export const breakpoints = {
  sm: '375px',  // iPhone 13 mini
  md: '768px',
  lg: '1024px',
  xl: '1280px',
} as const;

// Component Specific Styles
export const components = {
  statusBar: {
    height: '88px',
    background: colors.background,
    textColor: colors.textPrimary,
  },
  
  bottomNavigation: {
    height: '68px',
    background: '#101010',
    borderColor: colors.border,
    activeColor: colors.primary,
    inactiveColor: '#4E4F53',
  },
  
  card: {
    background: colors.cardBackground,
    borderColor: colors.border,
    borderRadius: borderRadius.lg,
    padding: spacing[6],
  },

  button: {
    primary: {
      background: colors.primary,
      color: colors.textPrimary,
      borderRadius: borderRadius.lg,
      padding: `${spacing[3]} ${spacing[6]}`,
    },
    secondary: {
      background: colors.cardBackground,
      color: colors.textSecondary,
      borderColor: colors.border,
      borderRadius: borderRadius.lg,
      padding: `${spacing[3]} ${spacing[6]}`,
    },
  },

  input: {
    background: colors.cardBackground,
    borderColor: colors.border,
    borderRadius: borderRadius.base,
    padding: spacing[3],
    color: colors.textPrimary,
  },
} as const;

// Animation Presets
export const animations = {
  fast: '150ms ease-in-out',
  normal: '300ms ease-in-out',
  slow: '500ms ease-in-out',
  
  slideUp: 'slideUp 300ms ease-out',
  fadeIn: 'fadeIn 300ms ease-in-out',
  scaleIn: 'scaleIn 200ms ease-out',
} as const;

// Layout Constants
export const layout = {
  containerMaxWidth: '375px',
  headerHeight: '88px',
  footerHeight: '68px',
  contentPadding: spacing[4],
  sectionSpacing: spacing[6],
} as const;
