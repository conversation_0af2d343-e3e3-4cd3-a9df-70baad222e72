import React, { useState, useEffect } from 'react';

interface StartupPageProps {
  onComplete: () => void;
}

export const StartupPage: React.FC<StartupPageProps> = ({ onComplete }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [showContinueHint, setShowContinueHint] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const [countdown, setCountdown] = useState(3);

  useEffect(() => {
    // Simulate loading time (2.5 seconds)
    const loadingTimer = setTimeout(() => {
      setIsLoading(false);
      setShowContinueHint(true);

      // Start countdown
      let count = 3;
      const countdownInterval = setInterval(() => {
        count--;
        setCountdown(count);

        if (count <= 0) {
          clearInterval(countdownInterval);
          handleExit();
        }
      }, 1000);

      return () => clearInterval(countdownInterval);
    }, 2500);

    return () => clearTimeout(loadingTimer);
  }, []);

  const handleExit = () => {
    setIsExiting(true);
    // Add a small delay for the fade-out animation
    setTimeout(() => {
      onComplete();
    }, 300);
  };

  const handleRefreshClick = () => {
    if (!isLoading && !isExiting) {
      handleExit();
    }
  };

  return (
    <div
      className={`relative w-full h-screen bg-black overflow-hidden transition-opacity duration-300 ${
        isExiting ? 'opacity-0' : 'opacity-100'
      }`}
      style={{ width: '375px', height: '812px' }}
    >
      {/* Status Bar */}
      <div className="absolute top-0 left-0 right-0 z-20 h-[88px] bg-black">
        <div className="absolute left-[74.44px] top-[48px] text-white text-[21px] font-normal" style={{ fontFamily: 'PingFang SC, sans-serif' }}>
          Tlock
        </div>
      </div>

      {/* Background Image Container */}
      <div className="absolute top-[88px] left-0 w-[375px] h-[724px] overflow-hidden">
        <img
          src="/images/bird-background.png"
          alt="Bird background"
          className="absolute -left-[15px] -top-[56px] w-[404px] h-[780px] object-cover"
        />
      </div>

      {/* Gradient Overlay */}
      <div
        className="absolute left-0 w-[375px] z-10"
        style={{
          top: '488px',
          height: '323.96px',
          background: 'linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 59.5%)'
        }}
      ></div>

      {/* Refresh Icon - Clickable when loading is complete */}
      <div
        className={`absolute z-15 transition-all duration-500 ${
          !isLoading && !isExiting ? 'cursor-pointer hover:scale-110 active:scale-95' : ''
        } ${isLoading ? 'animate-spin' : showContinueHint ? 'animate-pulse' : ''}`}
        style={{
          top: '320.49px',
          left: '117.01px',
          width: '140.97px',
          height: '140.63px'
        }}
        onClick={handleRefreshClick}
      >
        <img
          src="/images/refresh-icon.png"
          alt="Refresh"
          className="w-full h-full object-cover"
        />

        {/* Loading progress ring */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <svg className="w-full h-full -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="45"
                stroke="rgba(255,255,255,0.2)"
                strokeWidth="2"
                fill="none"
              />
              <circle
                cx="50"
                cy="50"
                r="45"
                stroke="#259AEE"
                strokeWidth="2"
                fill="none"
                strokeLinecap="round"
                strokeDasharray="283"
                strokeDashoffset="283"
                className="animate-[progress_2.5s_ease-in-out_forwards]"
              />
            </svg>
          </div>
        )}
      </div>

      {/* Main Text */}
      <div
        className="absolute z-20"
        style={{
          top: '637px',
          left: '49px',
          width: '277px',
          height: '96px'
        }}
      >
        <h1
          className="text-white font-bold text-center uppercase"
          style={{
            fontFamily: 'HarmonyOS Sans SC, sans-serif',
            fontSize: '36px',
            lineHeight: '1.32',
            letterSpacing: '0.1em'
          }}
        >
          Safeguard<br />Free Speech
        </h1>
      </div>

      {/* Loading indicator or tap hint */}
      {isLoading ? (
        <div className="absolute bottom-20 left-0 right-0 z-30 text-center">
          <div className="text-white/60 text-sm animate-pulse">Loading...</div>
        </div>
      ) : showContinueHint && !isExiting ? (
        <div className="absolute bottom-20 left-0 right-0 z-30 text-center">
          <div className="text-white/80 text-sm animate-pulse mb-3">Tap to continue</div>
          <div className="flex items-center justify-center space-x-2">
            <div className="text-white/50 text-xs">Auto-entering in</div>
            <div className="bg-primary-500/20 border border-primary-500 text-primary-500 px-2 py-1 rounded-lg text-xs font-bold min-w-[24px]">
              {countdown}
            </div>
            <div className="text-white/50 text-xs">second{countdown !== 1 ? 's' : ''}</div>
          </div>
        </div>
      ) : null}
    </div>
  );
};
