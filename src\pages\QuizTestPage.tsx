import React, { useState } from 'react';
import { Routes } from '../types/routes';
import { runQuizTests, testGetQuestions, testSubmitAnswers } from '../test/quiz.test';

interface QuizTestPageProps {
  user?: any;
  onNavigate?: (route: Routes) => void;
}

export const QuizTestPage: React.FC<QuizTestPageProps> = ({ user, onNavigate }) => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const runSingleTest = async (testName: string, testFunction: () => Promise<void>) => {
    try {
      setIsRunning(true);
      addResult(`🚀 Starting ${testName}...`);
      
      await testFunction();
      
      addResult(`✅ ${testName} completed successfully`);
    } catch (error) {
      addResult(`❌ ${testName} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsRunning(false);
    }
  };

  const runAllTests = async () => {
    try {
      setIsRunning(true);
      clearResults();
      addResult('🚀 Starting all Quiz API tests...');
      
      await runQuizTests();
      
      addResult('🎉 All tests completed successfully!');
    } catch (error) {
      addResult(`💥 Tests failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white flex flex-col">
      {/* Header */}
      <div className="h-16 flex items-center justify-between px-4" style={{ backgroundColor: '#000000' }}>
        <button
          onClick={() => onNavigate?.(Routes.EARN)}
          className="text-blue-500 hover:opacity-80"
        >
          ← Back
        </button>
        <h1 className="text-xl font-medium">Quiz API Test</h1>
        <div className="w-16"></div>
      </div>

      {/* Content */}
      <div className="flex-1 px-4 py-6">
        {/* Test Buttons */}
        <div className="space-y-4 mb-8">
          <button
            onClick={runAllTests}
            disabled={isRunning}
            className="w-full py-4 bg-blue-500 text-white rounded-2xl font-medium hover:opacity-80 transition-opacity disabled:opacity-50"
          >
            {isRunning ? 'Running Tests...' : 'Run All Tests'}
          </button>

          <div className="grid grid-cols-1 gap-3">
            <button
              onClick={() => runSingleTest('Get Questions', testGetQuestions)}
              disabled={isRunning}
              className="w-full py-3 bg-green-600 text-white rounded-xl font-medium hover:opacity-80 transition-opacity disabled:opacity-50"
            >
              Test Get Questions
            </button>

            <button
              onClick={() => runSingleTest('Submit Answers', testSubmitAnswers)}
              disabled={isRunning}
              className="w-full py-3 bg-orange-600 text-white rounded-xl font-medium hover:opacity-80 transition-opacity disabled:opacity-50"
            >
              Test Submit Answers
            </button>
          </div>

          <button
            onClick={clearResults}
            className="w-full py-3 bg-gray-600 text-white rounded-xl font-medium hover:opacity-80 transition-opacity"
          >
            Clear Results
          </button>
        </div>

        {/* Test Results */}
        <div className="bg-gray-900 rounded-2xl p-4">
          <h3 className="text-lg font-medium mb-4">Test Results:</h3>
          
          {testResults.length === 0 ? (
            <p className="text-gray-400 italic">No test results yet. Click a test button to start.</p>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg text-sm font-mono ${
                    result.includes('✅') ? 'bg-green-900 text-green-200' :
                    result.includes('❌') || result.includes('💥') ? 'bg-red-900 text-red-200' :
                    result.includes('⚠️') ? 'bg-yellow-900 text-yellow-200' :
                    result.includes('🚀') || result.includes('🎉') ? 'bg-blue-900 text-blue-200' :
                    'bg-gray-800 text-gray-300'
                  }`}
                >
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="mt-8 p-4 bg-gray-800 rounded-2xl">
          <h4 className="font-medium mb-2">Instructions:</h4>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• Make sure you're logged in before running tests</li>
            <li>• "Get Questions" tests the API endpoint for fetching quiz questions</li>
            <li>• "Submit Answers" tests submitting quiz answers and getting results</li>
            <li>• Check the console for detailed API responses</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
