/**
 * Core type definitions for Tlock Mini App
 */

import { ReactNode } from 'react';

// User related types
export interface User {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  photo_url?: string;
}

// Mining related types
export interface MiningData {
  level: number;
  coins: number;
  experience: number;
  mining_rate: number;
  last_mining_time: string;
  daily_limit: number;
  current_daily_mined: number;
}

export interface MiningSession {
  id: string;
  user_id: number;
  start_time: string;
  end_time?: string;
  coins_earned: number;
  status: 'active' | 'completed' | 'cancelled';
}

// Friends and referral types
export interface Friend {
  id: number;
  user: User;
  invited_at: string;
  coins_earned_from_referral: number;
  is_active: boolean;
}

export interface ReferralStats {
  total_friends: number;
  active_friends: number;
  total_coins_earned: number;
  referral_code: string;
}

// Task related types
export interface Task {
  id: string;
  title: string;
  description: string;
  reward_coins: number;
  task_type: 'daily' | 'social' | 'referral' | 'special';
  requirements: {
    action: string;
    target?: string;
    count?: number;
  };
  status: 'available' | 'in_progress' | 'completed' | 'claimed';
  expires_at?: string;
  icon?: string;
}

// UI Component types
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface CardProps extends BaseComponentProps {
  title?: string;
  subtitle?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  border?: boolean;
  shadow?: boolean;
}

// Navigation types
export interface NavigationItem {
  id: string;
  label: string;
  icon: ReactNode;
  path: string;
  badge?: number;
  active?: boolean;
}

// API related types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
  };
}

// App state types
export interface AppState {
  user: User | null;
  mining: MiningData | null;
  friends: Friend[];
  tasks: Task[];
  referral_stats: ReferralStats | null;
  is_loading: boolean;
  error: string | null;
}

// Theme types
export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
}

// Utility types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export type SortOrder = 'asc' | 'desc';

export interface SortConfig<T> {
  key: keyof T;
  order: SortOrder;
}

// Event types
export interface MiningEvent {
  type: 'start' | 'stop' | 'reward';
  timestamp: string;
  data: any;
}

export interface NotificationEvent {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}
