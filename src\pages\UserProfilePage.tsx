import React from 'react';
import { Routes } from '../types/routes';
import { useUser } from '../contexts/UserContext';

// 导入Figma资源 - 严格按照设计图顺序
import profileUserAvatar from '../assets/figma/profile_user_avatar.png';
import levelIcon1_10 from '../assets/figma/level_icon_1_10.svg';        // 第1个：绿色宝石 ✅
import levelIcon11_20 from '../assets/figma/level_icon_11_20.svg';      // 第2个：蓝色宝石 ✅
import levelIcon21_30 from '../assets/figma/level_icon_21_30_purple.svg'; // 第3个：紫色宝石 (新创建)
import levelIcon31_40 from '../assets/figma/level_icon_21_30.svg';       // 第4个：黄色宝石 (重新映射)
import levelIcon41_50 from '../assets/figma/level_icon_31_40.svg';       // 第5个：彩虹宝石 (重新映射)
import currentLevelIcon from '../assets/figma/current_level_lv16.svg';
import lightEffect1 from '../assets/figma/light_effect_1.png';
import levelBg1_10 from '../assets/figma/level_bg_1_10.png';

interface UserProfilePageProps {
  user?: any;
  onNavigate: (route: Routes) => void;
}

const UserProfilePage: React.FC<UserProfilePageProps> = ({ user, onNavigate }) => {
  const { userData } = useUser();

  // 使用真实用户数据，如果没有则使用默认值
  const userLevel = userData?.level || 16;
  const username = userData?.username || 'Kristin Watson';
  const userId = userData?.id || 'test123';
  const avatar = userData?.avatar || profileUserAvatar;

  // 用户等级数据（这些可能需要从其他API获取，暂时使用计算值）
  const userXP = 9020;
  const nextLevelXP = 10800;
  const xpProgress = (userXP / nextLevelXP) * 100; // 百分比进度

  // 返回上一页
  const handleBack = () => {
    onNavigate(Routes.MINING);
  };

  return (
    <div className="flex flex-col min-h-screen bg-black text-white">


      {/* User Profile Section */}
      <div className="flex flex-col items-center mt-8">
        {/* User Avatar */}
        <div className="w-[60px] h-[60px] rounded-full overflow-hidden">
          <img
            src={avatar}
            alt={username}
            className="w-full h-full object-cover"
          />
        </div>

        {/* User Name */}
        <h2
          className="mt-3 text-center"
          style={{
            fontFamily: 'PingFang SC',
            fontSize: '15px',
            color: 'rgba(255, 255, 255, 0.5)',
            lineHeight: '1em'
          }}
        >
          {username}
        </h2>

        {/* User ID */}
        <p
          className="mt-1 text-center"
          style={{
            fontFamily: 'PingFang SC',
            fontSize: '12px',
            color: 'rgba(255, 255, 255, 0.3)',
            lineHeight: '1em'
          }}
        >
          ID: {userId}
        </p>
        
        {/* Level Badge */}
        <div className="flex items-center mt-3 space-x-1">
          <div
            className="rounded-xl px-1.5 py-1 flex items-center space-x-1"
            style={{
              backgroundColor: 'rgba(118, 197, 255, 0.3)',
              borderRadius: '12px'
            }}
          >
            {/* 当前等级图标 */}
            <img
              src={currentLevelIcon}
              alt={`Level ${userLevel}`}
              className="w-[9px] h-[9px]"
            />
            <span
              className="font-bold text-xs"
              style={{
                fontFamily: 'Roboto',
                fontSize: '10px',
                color: '#DBF0FF'
              }}
            >
              {userLevel}
            </span>
          </div>
          
          <span 
            className="text-sm"
            style={{ 
              fontFamily: 'PingFang SC',
              fontSize: '15px',
              color: '#7DCCF3',
              lineHeight: '1.5em'
            }}
          >
            {userXP}/{nextLevelXP}
          </span>
        </div>
        
        {/* XP Progress Bar */}
        <div className="mt-6 w-[274px] h-[9px] relative">
          {/* Background */}
          <div 
            className="absolute inset-0 rounded-lg"
            style={{ 
              backgroundColor: '#13171C',
              borderRadius: '18px'
            }}
          ></div>
          
          {/* Progress */}
          <div 
            className="absolute top-0 left-0 h-full rounded-lg"
            style={{ 
              width: `${xpProgress}%`,
              borderRadius: '6px',
              background: 'linear-gradient(to right, #259AEE, #7AFFFF)'
            }}
          ></div>
        </div>
        
        {/* Description Text */}
        <p 
          className="mt-14 px-4 text-center"
          style={{ 
            fontFamily: 'PingFang SC',
            fontSize: '14px',
            color: 'rgba(255, 255, 255, 0.5)',
            lineHeight: '1.4em'
          }}
        >
          Hey, check out that progress bar! Want to fill it up and level up?
          Just check in daily and knock out some quick missions for coins.
          Higher levels mean shinier, more epic gems!
        </p>
      </div>

      {/* Level Chart Section - 修正后的图表 */}
      <div className="relative mt-16 px-4">
        {/* 黑色背景卡片 */}
        <div
          className="w-full h-[200px] rounded-3xl relative mx-auto"
          style={{
            backgroundColor: '#000000',
            maxWidth: '343px',
            border: '1px solid rgba(255, 255, 255, 0.05)',
            borderRadius: '17px',
            padding: '20px'
          }}
        >
          {/* Level Bars - 从下到上增长 */}
          <div className="flex justify-between items-end h-[150px] relative">
            {/* Level 1-10 */}
            <div className="flex flex-col items-center">
              {/* 宝石图标 */}
              <img
                src={levelIcon1_10}
                alt="Level 1-10"
                className="w-[24px] h-[24px] mb-1"
              />

              {/* 数值 - 放在宝石图标下面，柱子上面 */}
              <span
                className="text-white mb-2"
                style={{
                  fontFamily: 'HarmonyOS Sans SC',
                  fontSize: '12px',
                  fontWeight: 500
                }}
              >
                0
              </span>

              {/* 柱子 */}
              <div
                className="w-[31px] h-[29px] relative"
                style={{
                  backgroundImage: `url(${levelBg1_10})`,
                  backgroundSize: 'cover'
                }}
              />
            </div>

            {/* Level 11-20 */}
            <div className="flex flex-col items-center">
              {/* 宝石图标 */}
              <img
                src={levelIcon11_20}
                alt="Level 11-20"
                className="w-[24px] h-[24px] mb-1"
              />

              {/* 数值 - 放在宝石图标下面，柱子上面 */}
              <span
                className="text-white mb-2"
                style={{
                  fontFamily: 'HarmonyOS Sans SC',
                  fontSize: '12px',
                  fontWeight: 500
                }}
              >
                6,600
              </span>

              {/* 柱子 */}
              <div
                className="w-[31px] h-[58px] relative"
                style={{
                  backgroundImage: `url(${levelBg1_10})`,
                  backgroundSize: 'cover'
                }}
              />
            </div>

            {/* Level 21-30 */}
            <div className="flex flex-col items-center">
              {/* 宝石图标 */}
              <img
                src={levelIcon21_30}
                alt="Level 21-30"
                className="w-[24px] h-[24px] mb-1"
              />

              {/* 数值 - 放在宝石图标下面，柱子上面 */}
              <span
                className="text-white mb-2"
                style={{
                  fontFamily: 'HarmonyOS Sans SC',
                  fontSize: '12px',
                  fontWeight: 500
                }}
              >
                22,355
              </span>

              {/* 柱子 */}
              <div
                className="w-[31px] h-[78px] relative"
                style={{
                  backgroundImage: `url(${levelBg1_10})`,
                  backgroundSize: 'cover'
                }}
              />
            </div>

            {/* Level 31-40 */}
            <div className="flex flex-col items-center">
              {/* 宝石图标 */}
              <img
                src={levelIcon31_40}
                alt="Level 31-40"
                className="w-[24px] h-[24px] mb-1"
              />

              {/* 数值 - 放在宝石图标下面，柱子上面 */}
              <span
                className="text-white mb-2"
                style={{
                  fontFamily: 'HarmonyOS Sans SC',
                  fontSize: '12px',
                  fontWeight: 500
                }}
              >
                93,900
              </span>

              {/* 柱子 */}
              <div
                className="w-[31px] h-[98px] relative"
                style={{
                  backgroundImage: `url(${levelBg1_10})`,
                  backgroundSize: 'cover'
                }}
              />
            </div>

            {/* Level 41-50 */}
            <div className="flex flex-col items-center">
              {/* 宝石图标 */}
              <img
                src={levelIcon41_50}
                alt="Level 41-50"
                className="w-[24px] h-[24px] mb-1"
              />

              {/* 数值 - 放在宝石图标下面，柱子上面 */}
              <span
                className="text-white mb-2"
                style={{
                  fontFamily: 'HarmonyOS Sans SC',
                  fontSize: '12px',
                  fontWeight: 500
                }}
              >
                175,200
              </span>

              {/* 柱子 */}
              <div
                className="w-[31px] h-[118px] relative"
                style={{
                  backgroundImage: `url(${levelBg1_10})`,
                  backgroundSize: 'cover'
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfilePage;
