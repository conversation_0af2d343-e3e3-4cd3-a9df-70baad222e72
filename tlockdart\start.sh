#!/bin/bash

# TLock Telegram Bot 启动脚本
# 使用方法: ./start.sh [dev|prod]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Dart 是否安装
check_dart() {
    if ! command -v dart &> /dev/null; then
        log_error "Dart 未安装，请先安装 Dart SDK"
        exit 1
    fi
    log_success "Dart 已安装: $(dart --version)"
}

# 检查环境变量
check_env() {
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，使用默认配置"
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_info "已复制 .env.example 到 .env，请编辑配置"
        fi
    fi
    
    # 加载环境变量
    if [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    # 检查必要的环境变量
    if [ -z "$TELEGRAM_BOT_TOKEN" ] || [ "$TELEGRAM_BOT_TOKEN" = "your_bot_token_here" ]; then
        log_error "请在 .env 文件中设置正确的 TELEGRAM_BOT_TOKEN"
        exit 1
    fi
}

# 安装依赖
install_deps() {
    log_info "安装依赖..."
    dart pub get
    log_success "依赖安装完成"
}

# 开发模式运行
run_dev() {
    log_info "以开发模式启动机器人..."
    dart run bin/teledart_bot.dart
}

# 生产模式运行
run_prod() {
    log_info "编译可执行文件..."
    dart compile exe bin/teledart_bot.dart -o tlock_bot
    log_success "编译完成"
    
    log_info "以生产模式启动机器人..."
    ./tlock_bot
}

# 主函数
main() {
    local mode=${1:-dev}
    
    log_info "TLock Telegram Bot 启动脚本"
    log_info "模式: $mode"
    
    # 检查环境
    check_dart
    check_env
    install_deps
    
    # 根据模式运行
    case $mode in
        "dev")
            run_dev
            ;;
        "prod")
            run_prod
            ;;
        *)
            log_error "未知模式: $mode"
            log_info "使用方法: $0 [dev|prod]"
            exit 1
            ;;
    esac
}

# 信号处理
trap 'log_info "正在停止机器人..."; exit 0' SIGINT SIGTERM

# 运行主函数
main "$@"
