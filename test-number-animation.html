<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字动画测试</title>
    <style>
        body {
            font-family: 'HarmonyOS Sans SC', sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #4CAF50;
        }
        .number-display {
            font-size: 32px;
            font-weight: bold;
            background: black;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 10px 0;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #45a049;
        }
        .info {
            font-size: 14px;
            color: #ccc;
            margin: 10px 0;
        }
        .problem-demo {
            background: #3a1a1a;
            border: 1px solid #ff4444;
        }
        .fixed-demo {
            background: #1a3a1a;
            border: 1px solid #44ff44;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>数字翻转动画测试</h1>
        <p class="info">测试小数部分在翻转动画时的显示问题</p>

        <div class="test-section problem-demo">
            <div class="test-title">问题演示 - 原始实现</div>
            <div class="info">这里会展示动画结束后小数部分显示错误的问题</div>
            <div class="number-display" id="problem-display">1,234.56</div>
            <div class="controls">
                <button onclick="testProblem(1234.56)">1,234.56</button>
                <button onclick="testProblem(1234.78)">1,234.78</button>
                <button onclick="testProblem(1235.12)">1,235.12</button>
                <button onclick="testProblem(1299.99)">1,299.99</button>
                <button onclick="testProblem(1300.01)">1,300.01</button>
            </div>
        </div>

        <div class="test-section fixed-demo">
            <div class="test-title">修复后 - 新实现</div>
            <div class="info">修复后的版本应该正确显示小数部分</div>
            <div class="number-display" id="fixed-display">1,234.56</div>
            <div class="controls">
                <button onclick="testFixed(1234.56)">1,234.56</button>
                <button onclick="testFixed(1234.78)">1,234.78</button>
                <button onclick="testFixed(1235.12)">1,235.12</button>
                <button onclick="testFixed(1299.99)">1,299.99</button>
                <button onclick="testFixed(1300.01)">1,300.01</button>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">精度测试</div>
            <div class="info">测试浮点数精度问题</div>
            <div class="number-display" id="precision-display">0.00</div>
            <div class="controls">
                <button onclick="testPrecision(0.1 + 0.2)">0.1 + 0.2</button>
                <button onclick="testPrecision(1.005 * 100)">1.005 * 100</button>
                <button onclick="testPrecision(123.456789)">123.456789</button>
                <button onclick="testPrecision(999.999)">999.999</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟原始的格式化函数（有问题的版本）
        function problemFormatNumber(num) {
            return num.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        // 修复后的格式化函数
        function fixedFormatNumber(num) {
            const rounded = Math.round(num * 100) / 100;
            return rounded.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        // 简单的动画效果模拟
        function animateNumber(elementId, targetValue, formatFunction) {
            const element = document.getElementById(elementId);
            const currentText = element.textContent;
            const currentValue = parseFloat(currentText.replace(/,/g, ''));
            
            // 模拟动画过程
            element.style.color = '#ffaa00';
            element.textContent = '动画中...';
            
            setTimeout(() => {
                // 动画结束，显示最终值
                element.textContent = formatFunction(targetValue);
                element.style.color = 'white';
            }, 800);
        }

        function testProblem(value) {
            console.log('Problem test:', value, '->', problemFormatNumber(value));
            animateNumber('problem-display', value, problemFormatNumber);
        }

        function testFixed(value) {
            console.log('Fixed test:', value, '->', fixedFormatNumber(value));
            animateNumber('fixed-display', value, fixedFormatNumber);
        }

        function testPrecision(value) {
            console.log('Precision test:', value);
            console.log('Original:', problemFormatNumber(value));
            console.log('Fixed:', fixedFormatNumber(value));
            
            const element = document.getElementById('precision-display');
            element.innerHTML = `
                <div style="font-size: 16px; color: #ff4444;">原始: ${problemFormatNumber(value)}</div>
                <div style="font-size: 16px; color: #44ff44;">修复: ${fixedFormatNumber(value)}</div>
                <div style="font-size: 14px; color: #ccc;">原始值: ${value}</div>
            `;
        }

        // 自动测试一些常见的精度问题
        window.onload = function() {
            console.log('=== 精度问题测试 ===');
            const testValues = [0.1 + 0.2, 1.005 * 100, 123.456789, 999.999];
            testValues.forEach(val => {
                console.log(`值: ${val}`);
                console.log(`原始格式化: ${problemFormatNumber(val)}`);
                console.log(`修复格式化: ${fixedFormatNumber(val)}`);
                console.log('---');
            });
        };
    </script>
</body>
</html>
