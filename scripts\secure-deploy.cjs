#!/usr/bin/env node

/**
 * Tlock 安全部署脚本
 * 确保所有安全配置正确应用
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const PRODUCTION_URL = 'https://tg.tlock.org';

function runCommand(command, description) {
  console.log(`\n🔧 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} 完成`);
    return true;
  } catch (error) {
    console.log(`❌ ${description} 失败:`, error.message);
    return false;
  }
}

function checkSecurityConfig() {
  console.log('\n🔒 检查安全配置...');
  
  // 检查机器人代码是否包含安全功能
  const botFile = path.join(process.cwd(), 'tlockdart/lib/teledart_bot.dart');
  if (fs.existsSync(botFile)) {
    const content = fs.readFileSync(botFile, 'utf8');
    
    const securityFeatures = [
      { name: '访问频率控制', pattern: 'checkAccessRate' },
      { name: '邀请码格式验证', pattern: 'startsWith(\'ref_\')' },
      { name: '访问日志记录', pattern: '访问日志' },
      { name: '安全时间戳', pattern: 'verified=true' },
    ];
    
    console.log('🔍 安全功能检查:');
    securityFeatures.forEach(feature => {
      const hasFeature = content.includes(feature.pattern);
      console.log(`   ${hasFeature ? '✅' : '❌'} ${feature.name}`);
    });
    
    return securityFeatures.every(feature => content.includes(feature.pattern));
  } else {
    console.log('❌ 机器人代码文件未找到');
    return false;
  }
}

function checkWebAppSecurity() {
  console.log('\n🔒 检查 Web App 安全配置...');
  
  const apiFile = path.join(process.cwd(), 'src/services/api.ts');
  if (fs.existsSync(apiFile)) {
    const content = fs.readFileSync(apiFile, 'utf8');
    
    const securityFeatures = [
      { name: '验证参数检查', pattern: 'verified !== \'true\'' },
      { name: '时间戳验证', pattern: '10 * 60 * 1000' },
      { name: '安全日志记录', pattern: '安全验证' },
    ];
    
    console.log('🔍 Web App 安全功能检查:');
    securityFeatures.forEach(feature => {
      const hasFeature = content.includes(feature.pattern);
      console.log(`   ${hasFeature ? '✅' : '❌'} ${feature.name}`);
    });
    
    return securityFeatures.every(feature => content.includes(feature.pattern));
  } else {
    console.log('❌ API 服务文件未找到');
    return false;
  }
}

async function main() {
  console.log('🔒 Tlock 安全部署\n');
  console.log(`🎯 目标域名: ${PRODUCTION_URL}`);
  
  // 1. 检查安全配置
  const botSecurityOk = checkSecurityConfig();
  const webAppSecurityOk = checkWebAppSecurity();
  
  if (!botSecurityOk || !webAppSecurityOk) {
    console.log('\n❌ 安全配置检查失败，请确保所有安全功能都已正确配置');
    process.exit(1);
  }
  
  console.log('\n✅ 安全配置检查通过');
  
  // 2. 构建应用
  if (!runCommand('npm run build', '构建应用')) {
    process.exit(1);
  }
  
  // 3. 更新机器人配置（安全模式）- 允许失败
  console.log('\n🔧 更新机器人安全配置...');
  try {
    execSync(`node scripts/update-bot-config.cjs ${PRODUCTION_URL}`, { stdio: 'inherit' });
    console.log('✅ 更新机器人安全配置 完成');
  } catch (error) {
    console.log('⚠️ 机器人配置更新失败，但继续部署:', error.message);
    console.log('💡 可以稍后手动运行: node scripts/test-bot-config.cjs');
  }
  
  // 4. 验证部署
  if (!runCommand('node scripts/verify-deployment.cjs', '验证部署状态')) {
    console.log('⚠️ 部署验证有问题，但继续...');
  }
  
  console.log('\n🎉 安全部署完成！');
  
  console.log('\n🔒 安全特性已启用：');
  console.log('✅ 所有访问都经过机器人服务验证');
  console.log('✅ 邀请码格式和有效性检查');
  console.log('✅ 访问频率限制（5分钟内最多10次）');
  console.log('✅ 访问时间戳验证（防重放攻击）');
  console.log('✅ 轻量级日志记录（仅打印）');
  console.log('✅ 用户安全教育和提醒');
  
  console.log('\n📋 安全部署后的测试步骤：');
  console.log('1. 启动机器人服务：cd tlockdart && dart run bin/teledart_bot.dart');
  console.log('2. 测试手动启动：在 @HabbyBabyBot 中发送 /start');
  console.log('3. 测试邀请链接：https://t.me/HabbyBabyBot?startapp=ref_TESTCODE');
  console.log('4. 检查控制台日志输出');
  console.log('5. 测试访问频率限制');
  console.log('6. 使用 /invites 查看内存统计');

  console.log('\n⚠️ 重要提醒：');
  console.log('- 确保机器人服务持续运行');
  console.log('- 邀请统计仅保存在内存中（重启后清空）');
  console.log('- 监控控制台日志输出');
  console.log('- 及时更新安全配置');
}

main().catch(error => {
  console.error('❌ 安全部署过程中发生错误:', error);
  process.exit(1);
});
