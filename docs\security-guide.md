# Tlock 安全架构指南

## 🔒 安全架构概述

Tlock 采用**经过机器人服务的安全架构**，确保所有用户访问都经过严格的安全验证。

### 安全流程图
```
用户点击邀请链接
    ↓
Telegram 验证用户身份
    ↓
机器人服务接收请求
    ↓
🔒 多层安全验证
    ↓
生成安全的 Web App URL
    ↓
用户安全进入小程序
```

## 🛡️ 安全特性

### 1. 身份验证
- ✅ **Telegram 官方验证**：所有请求都经过 Telegram 服务器验证
- ✅ **Bot Token 保护**：需要有效的 Bot Token 才能与机器人交互
- ✅ **用户身份确认**：确保用户身份真实性

### 2. 邀请码安全
- ✅ **格式验证**：检查邀请码必须以 `ref_` 开头
- ✅ **长度验证**：邀请码长度必须在 10-50 字符之间
- ✅ **有效性检查**：可扩展为检查邀请码是否在有效期内

### 3. 访问控制
- ✅ **频率限制**：5分钟内最多允许 10 次访问
- ✅ **时间戳验证**：防止重放攻击，链接10分钟后过期
- ✅ **异常检测**：记录和监控异常访问模式

### 4. 数据完整性
- ✅ **服务端生成URL**：所有 Web App URL 都由服务端生成
- ✅ **参数验证**：Web App 验证来自机器人的安全参数
- ✅ **加密传输**：所有通信都通过 HTTPS 加密

### 5. 审计和监控
- ✅ **轻量级日志记录**：在控制台记录所有访问尝试和安全事件
- ✅ **内存统计追踪**：在内存中记录邀请关系（重启后清空）
- ✅ **安全事件告警**：异常访问会被记录和标记

## 🔧 安全配置

### 机器人服务安全配置

```dart
// 访问频率控制
bool checkAccessRate(int userId) {
  // 5分钟内最多10次访问
  if (userHistory.length >= 10) {
    return false;
  }
  return true;
}

// 邀请码验证
if (!invitationCode.startsWith('ref_')) {
  // 拒绝无效格式的邀请码
  return;
}

// 安全URL生成
var appUrl = '$webAppUrl?id=${user.id}&verified=true&timestamp=$timestamp';
```

### Web App 安全验证

```typescript
// 验证来源安全性
const verified = urlParams.get('verified');
const timestamp = urlParams.get('timestamp');

if (verified !== 'true') {
  console.warn('⚠️ 未经验证的访问');
}

// 时间戳验证
const timeDiff = Math.abs(Date.now() - parseInt(timestamp));
if (timeDiff > 10 * 60 * 1000) {
  console.warn('⚠️ 访问链接已过期');
}
```

## 🚨 安全威胁防护

### 防护的威胁类型

1. **邀请码伪造**
   - 格式验证确保邀请码符合规范
   - 服务端验证确保邀请码有效性

2. **用户身份伪造**
   - Telegram 官方身份验证
   - 服务端用户信息验证

3. **重放攻击**
   - 时间戳验证防止链接重复使用
   - 访问频率限制防止暴力攻击

4. **数据篡改**
   - 服务端生成所有关键参数
   - Web App 验证参数完整性

## 📊 安全监控

### 日志记录内容

```
🔒 访问日志: {
  timestamp: "2025-01-18T10:30:00.000Z",
  user_id: 123456789,
  username: "user123",
  chat_id: 123456789,
  invitation_code: "ref_ABC123",
  user_agent: "telegram_bot"
}

🔗 邀请关系记录: {
  inviter_code: "ref_ABC123",
  invitee_id: 987654321,
  invitee_username: "newuser",
  timestamp: "2025-01-18T10:30:00.000Z"
}
```

### 异常检测指标

- 访问频率异常（短时间内大量访问）
- 邀请码格式异常
- 时间戳异常（过期或未来时间）
- 用户行为异常

## 🔄 部署和维护

### 安全部署命令

```bash
# 安全部署（推荐）
npm run deploy:secure

# 验证部署状态
npm run verify:prod
```

### 日常维护

1. **监控日志**：定期检查安全日志
2. **更新配置**：及时更新安全参数
3. **异常处理**：快速响应安全事件
4. **备份数据**：定期备份访问记录

## ⚠️ 安全最佳实践

### 开发阶段
- 所有安全功能都在开发环境测试
- 使用测试邀请码进行功能验证
- 定期进行安全代码审查

### 生产环境
- 确保机器人服务持续运行
- 监控服务器资源使用情况
- 定期更新依赖包和安全补丁
- 建立安全事件响应流程

### 用户教育
- 在欢迎消息中包含安全提示
- 提供安全帮助按钮和文档
- 及时通知用户安全相关信息

## 📞 安全支持

如果发现安全问题或需要安全支持：

1. **立即停止可疑操作**
2. **记录相关日志信息**
3. **联系技术团队**
4. **更新安全配置**

---

**记住：安全是一个持续的过程，需要不断监控、更新和改进。**
