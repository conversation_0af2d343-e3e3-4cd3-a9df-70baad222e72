import React, { useState, useEffect } from 'react';
import { Routes } from '../types/routes';
import { DocumentViewer } from '../components/DocumentViewer';
import { ENV_CONFIG, shouldUseEmbeddedViewer } from '../config/environment';
import { telegramWebApp } from '../utils/telegram';

interface DocumentsPageProps {
  user?: any;
  onNavigate: (route: Routes) => void;
}

export const DocumentsPage: React.FC<DocumentsPageProps> = ({ user, onNavigate }) => {
  const [showDocumentViewer, setShowDocumentViewer] = useState(false);

  useEffect(() => {
    console.log('DocumentsPage: 检查Telegram环境', {
      hasTelegram: !!window.Telegram,
      hasWebApp: !!window.Telegram?.WebApp,
      url: ENV_CONFIG.DOCUMENT.url
    });

    // 检查是否在Telegram Web App环境中
    if (window.Telegram?.WebApp) {
      console.log('DocumentsPage: 在小程序内显示文档查看器');
      // 在Telegram小程序中，使用内嵌文档查看器
      setShowDocumentViewer(true);
    } else {
      console.log('DocumentsPage: 使用window.open打开链接');
      // 在开发环境中，在新窗口打开
      window.open(ENV_CONFIG.DOCUMENT.url, '_blank');
      onNavigate(Routes.MINING);
    }
  }, [onNavigate]);

  const handleCloseDocument = () => {
    setShowDocumentViewer(false);
    onNavigate(Routes.MINING);
  };

  // 显示文档查看器
  if (showDocumentViewer) {
    return (
      <DocumentViewer
        url={ENV_CONFIG.DOCUMENT.url}
        title={ENV_CONFIG.DOCUMENT.title}
        onClose={handleCloseDocument}
      />
    );
  }

  return (
    <div className="min-h-screen bg-black text-white flex flex-col items-center justify-center">
      <div className="text-center">
        <div className="text-6xl mb-4">📚</div>
        <h1 className="text-2xl font-bold mb-2">Opening Documentation...</h1>
        <p className="text-gray-400">
          Redirecting to external documentation
        </p>
      </div>
    </div>
  );
};
