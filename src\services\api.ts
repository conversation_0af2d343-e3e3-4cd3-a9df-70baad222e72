/**
 * API Service
 * Handles all API requests to the backend
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { telegramWebApp, getTelegramUser, getTelegramInitData } from '../utils/telegram';
import { telegramService } from './telegram';

// API response interface
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// 登录接口参数类型
export interface LoginParams {
  id: string;
  first_name: string;
  last_name: string;
  username: string;
  auth_date: number;
  hash: string;
  invitationCode: string;
}

// 登录响应数据类型
export interface LoginResponse {
  code: number;
  data: {
    id: string;
    username: string;
    avatar: string;
    online: boolean;
    userBalance: number;
    userRate: number;
    baseSpeed: number;
    lastSettlementTime: number;
    userClaimEndTime: number;
    token: string;
    invitationCode: string;
    level: number;
    nationality: string;
  };
  msg: string;
}

// 用户数据类型
export interface UserData {
  id: string;
  username: string;
  avatar: string;
  online: boolean;
  userBalance: number;
  userRate: number;
  baseSpeed: number;
  lastSettlementTime: number;
  userClaimEndTime: number;
  token: string;
  invitationCode: string;
  level: number;
  nationality: string;
}

// 聊天室消息接口类型
export interface ChatRoomMessage {
  id: string;
  username: string;
  level: number;
  message: string;
  timestamp: string;
  avatar?: string;
}

// 好友数据接口类型
export interface FriendData {
  id: string;
  pid: string;
  online: boolean;
  username: string;
  registerTime: number;
}

// 任务奖励状态接口类型
export interface TaskRewardStatus {
  isReward: boolean;
  rewardAmount: number;
}

// 聊天室消息响应类型
export interface ChatRoomMessagesResponse {
  code: number;
  data: ChatRoomMessage[];
  msg: string;
}

// 签到状态接口类型
export interface StreakStatus {
  uid: string;
  completedDays: number;
  todayClaimed: boolean;
  nextReward: number;
}

// 签到状态响应类型
export interface StreakStatusResponse {
  code: number;
  data: StreakStatus;
  msg: string;
}

// 签到奖励响应类型
export interface ClaimStreakRewardResponse {
  code: number;
  data: any; // 根据实际API返回调整
  msg: string;
}

// Quiz相关接口类型
export interface QuizQuestion {
  question: string;
  options: {
    A: string;
    B: string;
    C: string;
    D: string;
  };
}

export interface QuizQuestionsResponse {
  code: number;
  data: QuizQuestion[];
  msg: string;
}

export interface QuizAnswer {
  answer: string;
}

export interface SubmitAnswersRequest {
  answers: QuizAnswer[];
}

export interface SubmitAnswersResponse {
  code: number;
  data: {
    uid: string;
    totalScore: number;
    rewardAmount: number;
  };
  msg: string;
}

// 存储token的key
const TOKEN_STORAGE_KEY = 'tlock_token';

class ApiService {
  private static instance: ApiService;
  private api: AxiosInstance;
  private token: string | null = null;

  private constructor() {
    // 在开发环境使用代理，生产环境使用完整URL
    const baseURL = import.meta.env.DEV
      ? '/api'
      : (import.meta.env.VITE_API_BASE_URL || 'https://api.tlock.xyz/api');
    const timeout = Number(import.meta.env.VITE_API_TIMEOUT) || 10000;

    // 从localStorage获取token
    this.token = localStorage.getItem(TOKEN_STORAGE_KEY);
    
    this.api = axios.create({
      baseURL,
      timeout,
      headers: {
        'Content-Type': 'application/json',
      },
      // 添加CORS配置
      withCredentials: false,
    });
    
    this.setupInterceptors();
  }
  
  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }
  
  private setupInterceptors(): void {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        // Add token to every request
        if (this.token && config.headers) {
          config.headers['token'] = this.token;
        }

        // Add Telegram init data to every request
        const initData = telegramService.getInitData();
        if (initData && config.headers) {
          config.headers['X-Telegram-Init-Data'] = initData;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    
    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        // Handle API errors
        if (error.response) {
          // Server responded with an error status
          // Show error message to user
          const errorMessage = error.response.data.message || 'An error occurred. Please try again.';
          telegramService.showAlert(errorMessage);
        } else if (error.request) {
          // Request was made but no response received (可能是CORS错误)
          // 检查是否是CORS错误
          if (error.message.includes('CORS') || error.message.includes('cross-origin')) {
            telegramService.showAlert('CORS error detected. Please contact support or try again later.');
          } else {
            telegramService.showAlert('Network error. Please check your connection.');
          }
        } else {
          // Error in setting up the request
          telegramService.showAlert('An unexpected error occurred.');
        }
        
        return Promise.reject(error);
      }
    );
  }

  // Token management methods
  public setToken(token: string): void {
    this.token = token;
    localStorage.setItem(TOKEN_STORAGE_KEY, token);
  }

  public getToken(): string | null {
    return this.token;
  }

  public getBaseURL(): string {
    return this.api.defaults.baseURL || '';
  }

  public clearToken(): void {
    this.token = null;
    localStorage.removeItem(TOKEN_STORAGE_KEY);
  }

  // 登录接口
  public async login(params: LoginParams): Promise<UserData> {
    try {
      // 使用axios实例进行登录请求，这样会使用代理
      const response = await this.api.post<LoginResponse>('/mining/login', params, {
        headers: {
          'token': '' // 登录时token为空
        }
      });

      const result = response.data;

      if (result.code !== 0) {
        throw new Error(result.msg || 'Login failed');
      }

      // 保存token
      this.setToken(result.data.token);

      return result.data;
    } catch (error) {
      throw error;
    }
  }

  // 获取聊天室消息接口
  public async getChatRoomMessages(): Promise<ChatRoomMessage[]> {
    try {
      if (!this.token) {
        throw new Error('No token available. Please login first.');
      }

      const response = await this.api.get<ChatRoomMessagesResponse>('/mining/getChatRoomMessages', {
        headers: {
          'token': this.token
        }
      });

      const result = response.data;

      if (result.code !== 0) {
        throw new Error(result.msg || 'Failed to get chat room messages');
      }

      return result.data;
    } catch (error) {
      throw error;
    }
  }

  // 发送聊天室消息接口
  public async sendMessage(message: string): Promise<any> {
    try {
      if (!this.token) {
        throw new Error('No token available. Please login first.');
      }

      const response = await this.api.post('/mining/sendMessage', {
        message
      }, {
        headers: {
          'token': this.token
        }
      });

      const result = response.data;

      if (result.code !== 0) {
        throw new Error(result.msg || 'Failed to send message');
      }

      return result.data;
    } catch (error) {
      throw error;
    }
  }

  // 领取首页奖励接口
  public async claimHomeReward(): Promise<any> {
    try {
      if (!this.token) {
        throw new Error('No token available. Please login first.');
      }

      const response = await this.api.post('/mining/claimHomeReward', {}, {
        headers: {
          'token': this.token
        }
      });

      const result = response.data;

      if (result.code !== 0) {
        throw new Error(result.msg || 'Failed to claim home reward');
      }

      return result.data;
    } catch (error) {
      throw error;
    }
  }

  // 获取好友列表接口
  public async getFriends(): Promise<FriendData[]> {
    try {
      if (!this.token) {
        throw new Error('No token available. Please login first.');
      }

      const response = await this.api.get('/mining/getFriends', {
        headers: {
          'token': this.token
        }
      });

      const result = response.data;

      if (result.code !== 0) {
        throw new Error(result.msg || 'Failed to get friends list');
      }

      return result.data || [];
    } catch (error) {
      console.error('获取好友列表失败:', error);
      throw error;
    }
  }

  // 获取任务奖励状态接口
  public async getTaskRewardStatus(type: number): Promise<TaskRewardStatus> {
    try {
      if (!this.token) {
        throw new Error('No token available. Please login first.');
      }

      const response = await this.api.get(`/mining/getTaskRewardStatus?type=${type}`, {
        headers: {
          'token': this.token
        }
      });

      const result = response.data;

      if (result.code !== 0) {
        throw new Error(result.msg || 'Failed to get task reward status');
      }

      return result.data;
    } catch (error) {
      console.error(`获取任务奖励状态失败 (type=${type}):`, error);
      throw error;
    }
  }

  // 领取任务奖励接口
  public async claimTaskReward(type: number): Promise<any> {
    try {
      if (!this.token) {
        throw new Error('No token available. Please login first.');
      }

      const response = await this.api.post('/mining/claimTaskReward', { type }, {
        headers: {
          'token': this.token
        }
      });

      const result = response.data;

      if (result.code !== 0) {
        throw new Error(result.msg || 'Failed to claim task reward');
      }

      return result.data;
    } catch (error) {
      console.error(`领取任务奖励失败 (type=${type}):`, error);
      throw error;
    }
  }

  // 获取签到状态接口
  public async getStreakStatus(): Promise<StreakStatus> {
    try {
      if (!this.token) {
        throw new Error('No token available. Please login first.');
      }

      const response = await this.api.get<StreakStatusResponse>('/mining/getStreakStatus', {
        headers: {
          'token': this.token
        }
      });

      const result = response.data;

      if (result.code !== 0) {
        throw new Error(result.msg || 'Failed to get streak status');
      }

      return result.data;
    } catch (error) {
      throw error;
    }
  }

  // 领取签到奖励接口
  public async claimStreakReward(): Promise<any> {
    try {
      if (!this.token) {
        throw new Error('No token available. Please login first.');
      }

      const response = await this.api.post<ClaimStreakRewardResponse>('/mining/claimStreakReward', {}, {
        headers: {
          'token': this.token
        }
      });

      const result = response.data;

      if (result.code !== 0) {
        throw new Error(result.msg || 'Failed to claim streak reward');
      }

      return result.data;
    } catch (error) {
      throw error;
    }
  }

  // 获取Quiz题目接口
  public async getQuestions(): Promise<QuizQuestion[]> {
    try {
      if (!this.token) {
        throw new Error('No token available. Please login first.');
      }

      const response = await this.api.get<QuizQuestionsResponse>('/mining/getQuestions', {
        headers: {
          'token': this.token
        }
      });

      const result = response.data;

      if (result.code !== 0) {
        throw new Error(result.msg || 'Failed to get quiz questions');
      }

      return result.data;
    } catch (error) {
      throw error;
    }
  }

  // 提交Quiz答案接口
  public async submitAnswers(answers: QuizAnswer[]): Promise<any> {
    try {
      if (!this.token) {
        throw new Error('No token available. Please login first.');
      }

      const requestData: SubmitAnswersRequest = { answers };

      const response = await this.api.post<SubmitAnswersResponse>('/mining/submitAnswers', requestData, {
        headers: {
          'token': this.token
        }
      });

      const result = response.data;

      if (result.code !== 0) {
        throw new Error(result.msg || 'Failed to submit quiz answers');
      }

      return result.data;
    } catch (error) {
      throw error;
    }
  }

  // Generic request method
  private async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.api(config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
  
  // GET request
  public async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'GET',
      url,
      params,
    });
  }
  
  // POST request
  public async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'POST',
      url,
      data,
    });
  }
  
  // PUT request
  public async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PUT',
      url,
      data,
    });
  }
  
  // DELETE request
  public async delete<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'DELETE',
      url,
      params,
    });
  }
}

// Export singleton instance
export const apiService = ApiService.getInstance();

// 默认登录参数（用于非Telegram环境的测试）
export const DEFAULT_LOGIN_PARAMS: LoginParams = {
  id: "test123",
  first_name: "Test5",
  last_name: "User5",
  username: "TestUser5",
  auth_date: 1744026494,
  hash: "156a3f7780950d8d05fc63e4ce76ca5a2e81a836225d5132220984875602a220",
  invitationCode: "LIODQH"
};

// 从URL参数获取用户信息
export const getUrlParams = (): Partial<LoginParams> | null => {
  try {
    const urlParams = new URLSearchParams(window.location.search);
    const id = urlParams.get('id');
    const username = urlParams.get('username');
    const first_name = urlParams.get('first_name');
    const last_name = urlParams.get('last_name');
    const invitationCode = urlParams.get('invitationCode'); // 获取邀请码参数

    // 🔒 安全验证参数
    const verified = urlParams.get('verified');
    const timestamp = urlParams.get('timestamp');

    // 🔒 验证是否来自机器人服务
    const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';
    if (verified !== 'true' && !isDevelopment) {
      console.warn('⚠️ 未经验证的访问，可能存在安全风险');
    } else if (isDevelopment && verified !== 'true') {
      console.log('🔧 开发环境：跳过验证检查');
    }

    // 🔒 验证时间戳（防止重放攻击）
    if (timestamp) {
      const accessTime = parseInt(timestamp);
      const now = Date.now();
      const timeDiff = Math.abs(now - accessTime);

      // 如果时间差超过10分钟，可能是重放攻击
      if (timeDiff > 10 * 60 * 1000) {
        console.warn('⚠️ 访问链接已过期，可能存在安全风险');
      } else {
        console.log('✅ 访问时间戳验证通过');
      }
    }

    // 如果有基本的用户信息，返回参数对象
    if (id && first_name) {
      console.log('🔒 URL参数安全验证:', {
        id, username, first_name, last_name, invitationCode,
        verified: verified === 'true',
        timestampValid: timestamp ? 'checked' : 'missing'
      });

      return {
        id: id,
        first_name: first_name,
        last_name: last_name || "",
        username: username || "",
        // URL参数中没有这些信息，使用默认值
        auth_date: Math.floor(Date.now() / 1000),
        hash: "url_param_hash",
        invitationCode: invitationCode || "" // 使用URL中的邀请码
      };
    }
  } catch (error) {
    console.warn('获取URL参数失败:', error);
  }

  return null;
};

// 从Telegram Web App获取登录参数，如果获取不到则尝试URL参数，最后使用默认参数
export const getTelegramLoginParams = (): LoginParams => {
  try {
    // 1. 优先尝试从Telegram Web App获取
    const telegramUser = getTelegramUser();
    const initData = getTelegramInitData();

    if (telegramUser && initData) {
      console.log('使用Telegram Web App数据');
      return {
        id: telegramUser.id.toString(),
        first_name: telegramUser.first_name,
        last_name: telegramUser.last_name || "",
        username: telegramUser.username || "",
        auth_date: initData.auth_date,
        hash: initData.hash,
        invitationCode: telegramWebApp.getStartParam() || ""
      };
    }

    // 2. 如果Telegram数据获取失败，尝试从URL参数获取
    const urlParams = getUrlParams();
    if (urlParams && urlParams.id && urlParams.first_name) {
      console.log('使用URL参数数据');
      return {
        id: urlParams.id,
        first_name: urlParams.first_name,
        last_name: urlParams.last_name || "",
        username: urlParams.username || "",
        auth_date: urlParams.auth_date || Math.floor(Date.now() / 1000),
        hash: urlParams.hash || "url_param_hash",
        invitationCode: urlParams.invitationCode || ""
      };
    }
  } catch (error) {
    console.warn('获取用户数据失败:', error);
  }

  // 3. 最后使用默认参数
  console.log('使用默认测试数据');
  return DEFAULT_LOGIN_PARAMS;
};
