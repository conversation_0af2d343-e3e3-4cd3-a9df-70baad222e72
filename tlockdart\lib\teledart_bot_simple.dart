import 'package:teledart/teledart.dart';
import 'package:teledart/telegram.dart';
import 'package:teledart/model.dart';
import 'dart:io';

Future<void> mainBot() async {
  // 从环境变量获取 Bo<PERSON>ken，提高安全性
  final token = Platform.environment['TELEGRAM_BOT_TOKEN'] ??
      '**********************************************'; // 默认值仅用于开发
  
  // 从环境变量获取小程序 URL
  final webAppUrl = Platform.environment['WEBAPP_URL'] ?? 'https://tg.tlock.org';
  
  print('正在启动 TLock Bot...');
  print('Token: ${token.substring(0, 10)}...');
  print('WebApp URL: $webAppUrl');
  
  try {
    final username = (await Telegram(token).getMe()).username;
    print('Bot 启动成功: @$username');

    final teledart = TeleDart(token, Event(username!));
    teledart.start();
    print('TeleDart 服务已启动');

    // /start 命令处理
    teledart.onCommand('start').listen((message) async {
      print('收到 /start 命令');
      
      final chatId = message.chat.id;
      final user = message.from;

      if (user == null) {
        await teledart.sendMessage(chatId, '❌ 无法获取用户信息，请重试。');
        return;
      }

      print('用户信息: ${user.id} - ${user.first_name}');

      // 构造小程序 URL
      final appUrl = '$webAppUrl?id=${user.id}&username=${Uri.encodeComponent(user.username ?? '')}&first_name=${Uri.encodeComponent(user.first_name)}&last_name=${Uri.encodeComponent(user.last_name ?? '')}';
      
      print('生成的小程序 URL: $appUrl');

      // 发送欢迎消息
      await teledart.sendMessage(
        chatId,
        "🎉 TLock is back! \n\nWelcome to TLock: a new world with its own blockchain, games, apps, and rewards 🚀",
        reply_markup: InlineKeyboardMarkup(inline_keyboard: [
          [
            InlineKeyboardButton(
              text: "🎭 启动 Tlock 🎭",
              web_app: WebAppInfo(url: appUrl),
            ),
          ],
        ]),
      );
      
      print('欢迎消息发送成功');
    });

    // /help 命令处理
    teledart.onCommand('help').listen((message) async {
      print('收到 /help 命令');
      await message.reply(
        '🤖 TLock Bot 帮助\n\n'
        '/start - 启动 TLock 小程序\n'
        '/help - 显示此帮助信息\n'
        '/status - 查看机器人状态',
      );
    });

    // /status 命令处理
    teledart.onCommand('status').listen((message) async {
      print('收到 /status 命令');
      await message.reply(
        '✅ TLock Bot 运行正常\n'
        '🌐 小程序地址: $webAppUrl\n'
        '⏰ 当前时间: ${DateTime.now()}',
      );
    });

    print('所有命令处理器已注册完成');
    print('机器人正在运行中...');
    
  } catch (e) {
    print('❌ Bot 启动失败: $e');
    print('请检查网络连接和 Bot Token 是否正确');
    exit(1);
  }
}
