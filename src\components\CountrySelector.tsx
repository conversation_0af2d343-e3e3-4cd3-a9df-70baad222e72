import React, { useState, useRef, useEffect } from 'react';
import { Country, COUNTRIES } from '../data/countries';
import { FlagIcon } from './FlagIcon';

interface CountrySelectorProps {
  selectedCountry: Country;
  onCountryChange: (country: Country) => void;
  isOpen: boolean;
  onToggle: () => void;
}

export const CountrySelector: React.FC<CountrySelectorProps> = ({
  selectedCountry,
  onCountryChange,
  isOpen,
  onToggle
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 过滤国家列表
  const filteredCountries = COUNTRIES.filter(country =>
    country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    country.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 点击外部关闭下拉菜单 - 移除此逻辑，由父组件处理

  const handleCountrySelect = (country: Country, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onCountryChange(country);
    onToggle();
    setSearchTerm('');
  };

  if (!isOpen) return null;

  return (
    <div
      ref={dropdownRef}
      className="absolute top-full left-0 right-0 mt-1 bg-[#13171C] rounded-2xl border border-[#2A2A2A] z-50 max-h-80 overflow-hidden shadow-lg"
      style={{
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)'
      }}
    >
      {/* 搜索框 */}
      <div className="p-3 border-b border-[#2A2A2A]">
        <div className="relative">
          <input
            type="text"
            placeholder="Search countries..."
            value={searchTerm}
            onChange={(e) => {
              e.stopPropagation();
              setSearchTerm(e.target.value);
            }}
            className="w-full bg-[#1A1A1A] text-white text-sm pl-9 pr-3 py-2 rounded-lg border border-[#2A2A2A] focus:outline-none focus:border-blue-500"
            autoFocus
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M21 21L16.65 16.65" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>
      </div>



      {/* 国家列表 */}
      <div className="max-h-60 overflow-y-auto">
        {filteredCountries.length === 0 ? (
          <div className="p-4 text-center text-gray-400 text-sm">
            No countries found
          </div>
        ) : (
          filteredCountries.map((country) => (
            <div
              key={country.code}
              className={`flex items-center justify-between p-3 hover:bg-[#1A1A1A] cursor-pointer transition-all duration-200 ${
                selectedCountry.code === country.code ? 'bg-[#1A1A1A] border-l-2 border-blue-500' : ''
              }`}
              onClick={(e) => handleCountrySelect(country, e)}
            >
              <div className="flex items-center space-x-3">
                {/* 国家简称 */}
                <span className="text-white text-sm font-medium w-8 font-mono">
                  {country.code}
                </span>

                {/* 国家全名 */}
                <span className="text-white text-sm flex-1">
                  {country.name}
                </span>
              </div>

              {/* 国旗 */}
              <div className="flex items-center ml-2">
                <FlagIcon countryCode={country.flag} size={20} />
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};
