import React, { useState, useEffect } from 'react';
import { Routes } from '../types/routes';
import { CountrySelector } from '../components/CountrySelector';
import { Country, getDefaultCountry } from '../data/countries';
import { FlagIcon } from '../components/FlagIcon';
import estoniaFlag from '../assets/figma/flag3.svg';

interface SettingsPageProps {
  user?: any;
  onNavigate: (route: Routes) => void;
}

export const SettingsPage: React.FC<SettingsPageProps> = ({ user, onNavigate }) => {
  const [selectedCountry, setSelectedCountry] = useState<Country>(getDefaultCountry());
  const [selectedLanguage, setSelectedLanguage] = useState('English');
  const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);

  const handleNationClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsCountryDropdownOpen(!isCountryDropdownOpen);
  };

  const handleCountryChange = (country: Country) => {
    setSelectedCountry(country);
    // TODO: 这里可以调用API保存用户的国家选择
    console.log('Selected country:', country);
  };

  const handleLanguageClick = () => {
    // Handle language selection
    console.log('Language selection clicked');
  };

  const handleBackClick = () => {
    onNavigate(Routes.MINING);
  };

  // 关闭下拉菜单当点击其他地方时
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      // 检查点击是否在Nation区域外
      if (isCountryDropdownOpen && !target.closest('.nation-container')) {
        setIsCountryDropdownOpen(false);
      }
    };

    if (isCountryDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isCountryDropdownOpen]);

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Settings Content - Router已经处理了StatusBar */}
      <div className="px-4 pt-2">
        {/* Nation Setting */}
        <div className="bg-[#13171C] rounded-2xl mb-2 relative nation-container">
          <div
            className="flex items-center justify-between p-4 cursor-pointer hover:bg-[#1A1A1A] transition-colors"
            onClick={handleNationClick}
          >
            <span className="text-[15px] font-normal">Nation</span>
            <div className="flex items-center space-x-3">
              {/* 国家简称 */}
              <span className="text-[15px] font-medium text-white font-mono">
                {selectedCountry.code}
              </span>

              {/* 国旗 */}
              <div className="flex items-center">
                <FlagIcon countryCode={selectedCountry.flag} size={22} />
              </div>

              {/* 下拉箭头 */}
              <div className="flex items-center ml-1">
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="none"
                  className={`transform transition-transform duration-200 ${isCountryDropdownOpen ? 'rotate-180' : ''}`}
                >
                  <path d="M6 9L12 15L18 9" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
          </div>

          {/* 国家选择下拉菜单 */}
          <CountrySelector
            selectedCountry={selectedCountry}
            onCountryChange={handleCountryChange}
            isOpen={isCountryDropdownOpen}
            onToggle={() => setIsCountryDropdownOpen(!isCountryDropdownOpen)}
          />
        </div>

        {/* Language Setting */}
        <div className="bg-[#13171C] rounded-2xl">
          <div
            className="flex items-center justify-between p-4 cursor-pointer"
            onClick={handleLanguageClick}
          >
            <span className="text-[15px] font-normal">Language</span>
            <span className="text-[15px] font-medium text-right">English</span>
          </div>
        </div>

        {/* 开发工具 - 开发环境专用 */}
        {process.env.NODE_ENV === 'development' && (
          <div className="space-y-3">
            <div className="bg-[#13171C] rounded-2xl border border-blue-500">
              <div
                className="flex items-center justify-between p-4 cursor-pointer"
                onClick={() => onNavigate(Routes.TELEGRAM_TEST)}
              >
                <span className="text-[15px] font-normal text-blue-400">🧪 Telegram Test</span>
                <span className="text-[15px] font-medium text-right text-blue-400">DEV</span>
              </div>
            </div>

            <div className="bg-[#13171C] rounded-2xl border border-yellow-500">
              <div
                className="flex items-center justify-between p-4 cursor-pointer"
                onClick={() => onNavigate(Routes.URL_PARAMS_TEST)}
              >
                <span className="text-[15px] font-normal text-yellow-400">🔗 URL Params Test</span>
                <span className="text-[15px] font-medium text-right text-yellow-400">DEV</span>
              </div>
            </div>

            <div className="bg-[#13171C] rounded-2xl border border-green-500">
              <div
                className="flex items-center justify-between p-4 cursor-pointer"
                onClick={() => onNavigate(Routes.MEDIA_GENERATOR)}
              >
                <span className="text-[15px] font-normal text-green-400">📱 媒体生成器</span>
                <span className="text-[15px] font-medium text-right text-green-400">TOOLS</span>
              </div>
            </div>

            <div className="bg-[#13171C] rounded-2xl border border-purple-500">
              <div
                className="flex items-center justify-between p-4 cursor-pointer"
                onClick={() => onNavigate(Routes.BOT_TEST)}
              >
                <span className="text-[15px] font-normal text-purple-400">🤖 机器人测试</span>
                <span className="text-[15px] font-medium text-right text-purple-400">BOT</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
