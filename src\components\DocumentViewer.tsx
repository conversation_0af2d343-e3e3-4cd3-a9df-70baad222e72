import React, { useState, useEffect } from 'react';

interface DocumentViewerProps {
  url: string;
  title?: string;
  onClose: () => void;
}

export const DocumentViewer: React.FC<DocumentViewerProps> = ({ 
  url, 
  title = "Document", 
  onClose 
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 模拟加载过程
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [url]);

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setError('Failed to load document');
  };

  return (
    <div className="fixed inset-0 z-50 bg-black flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-black border-b border-gray-800 flex-shrink-0">
        <button
          onClick={onClose}
          className="flex items-center space-x-2 text-white hover:opacity-80"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
          </svg>
          <span>Back</span>
        </button>

        <h1 className="text-lg font-medium text-white truncate flex-1 text-center mx-4">
          {title}
        </h1>

        <div className="w-16"></div> {/* Spacer for centering */}
      </div>

      {/* Content Area */}
      <div className="flex-1 relative">
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-white">Loading document...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black">
            <div className="text-center">
              <div className="text-6xl mb-4">⚠️</div>
              <p className="text-white mb-4">{error}</p>
              <button
                onClick={onClose}
                className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:opacity-80"
              >
                Go Back
              </button>
            </div>
          </div>
        )}

        {/* Document Content */}
        {!error && (
          <iframe
            src={url}
            className="w-full h-full border-0"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
            title={title}
            sandbox="allow-scripts allow-same-origin allow-popups allow-forms allow-top-navigation"
            allow="fullscreen"
          />
        )}
      </div>
    </div>
  );
};
