import React, { useState, useEffect } from 'react';

interface StatusBarProps {
  title?: string;
  showBackButton?: boolean;
  onBackClick?: () => void;
  rightElement?: React.ReactNode;
  transparent?: boolean;
  className?: string;
}

export const StatusBar: React.FC<StatusBarProps> = ({
  title = 'Tlock',
  showBackButton = false,
  onBackClick,
  rightElement,
  transparent = false,
  className = '',
}) => {
  // 添加时间状态
  const [currentTime, setCurrentTime] = useState<string>('');

  // 更新时间的效果
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      setCurrentTime(`${hours}:${minutes}`);
    };

    // 初始化时间
    updateTime();

    // 每分钟更新一次时间
    const interval = setInterval(updateTime, 60000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div
      className={`absolute top-0 left-0 right-0 z-20 h-[88px] ${transparent ? 'bg-transparent' : 'bg-black'} ${className}`}
    >
      {/* 系统状态栏 - 顶部44px */}
      <div className="flex items-center justify-between px-4 pt-3 h-[44px]">
        {/* 左侧 - 时间 */}
        <div className="text-white text-[15px] font-medium">
          {currentTime}
        </div>

        {/* 右侧 - 系统图标 */}
        <div className="flex items-center space-x-1">
          {/* 信号强度 */}
          <svg width="18" height="12" viewBox="0 0 18 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0" y="8" width="2" height="4" fill="white"/>
            <rect x="3" y="6" width="2" height="6" fill="white"/>
            <rect x="6" y="4" width="2" height="8" fill="white"/>
            <rect x="9" y="2" width="2" height="10" fill="white"/>
            <rect x="12" y="0" width="2" height="12" fill="white"/>
          </svg>

          {/* WiFi */}
          <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.5 12C8.32843 12 9 11.3284 9 10.5C9 9.67157 8.32843 9 7.5 9C6.67157 9 6 9.67157 6 10.5C6 11.3284 6.67157 12 7.5 12Z" fill="white"/>
            <path d="M7.5 7.5C9.98528 7.5 12 5.48528 12 3" stroke="white" strokeWidth="1.5" strokeLinecap="round"/>
            <path d="M7.5 4.5C8.88071 4.5 10 3.38071 10 2" stroke="white" strokeWidth="1.5" strokeLinecap="round"/>
          </svg>

          {/* 电池 */}
          <div className="flex items-center">
            <span className="text-white text-[15px] font-medium mr-1">27%</span>
            <svg width="24" height="12" viewBox="0 0 24 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="0.5" y="1.5" width="20" height="9" rx="1.5" stroke="white" strokeWidth="1"/>
              <rect x="2" y="3" width="5" height="6" fill="white"/>
              <rect x="21" y="4" width="2" height="4" rx="1" fill="white"/>
            </svg>
          </div>
        </div>
      </div>

      {/* 应用标题栏 - 底部44px */}
      <div className="flex items-center justify-between px-4 h-[44px]">
        {/* 左侧 - 关闭按钮或返回按钮 */}
        <div className="w-6 h-6 flex items-center justify-center">
          {showBackButton && onBackClick ? (
            <button
              onClick={onBackClick}
              className="w-6 h-6 flex items-center justify-center text-white"
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12.5 15L7.5 10L12.5 5"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          ) : (
            <button className="w-6 h-6 flex items-center justify-center text-white">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 5L5 15" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M5 5L15 15" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          )}
        </div>

        {/* 标题和下拉箭头 - 标题靠左，下拉箭头靠右 */}
        <div className="flex-1 flex justify-between items-center px-4">
          <h1
            className="text-white text-[16px] font-normal"
            style={{ fontFamily: 'PingFang SC, sans-serif' }}
          >
            Tlock
          </h1>
          {/* 下拉箭头 */}
          <svg
            width="12"
            height="8"
            viewBox="0 0 12 8"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M1 1L6 6L11 1" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>

        {/* 右侧 - 三个点菜单按钮或自定义元素 */}
        <div className="w-6 h-6 flex items-center justify-center">
          {rightElement || (
            <button className="w-6 h-6 flex items-center justify-center text-white">
              <svg width="4" height="16" viewBox="0 0 4 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="2" cy="2" r="2" fill="white"/>
                <circle cx="2" cy="8" r="2" fill="white"/>
                <circle cx="2" cy="14" r="2" fill="white"/>
              </svg>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

// Specialized status bar for different pages
interface HomeStatusBarProps {
  userAvatar?: string;
  onAvatarClick?: () => void;
  notificationCount?: number;
  onNotificationClick?: () => void;
}

export const HomeStatusBar: React.FC<HomeStatusBarProps> = ({
  userAvatar,
  onAvatarClick,
  notificationCount = 0,
  onNotificationClick,
}) => {
  return (
    <StatusBar
      title="Tlock"
      rightElement={
        <div className="flex items-center space-x-2">
          {/* Notification bell */}
          {onNotificationClick && (
            <button
              onClick={onNotificationClick}
              className="relative w-8 h-8 flex items-center justify-center text-white hover:bg-white/10 rounded-lg transition-colors"
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M15 6.5C15 5.17392 14.4732 3.90215 13.5355 2.96447C12.5979 2.02678 11.3261 1.5 10 1.5C8.67392 1.5 7.40215 2.02678 6.46447 2.96447C5.52678 3.90215 5 5.17392 5 6.5C5 12.5 2.5 14 2.5 14H17.5C17.5 14 15 12.5 15 6.5Z"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M11.4417 17.5C11.2952 17.7526 11.0849 17.9622 10.8319 18.1079C10.5789 18.2537 10.292 18.3304 10 18.3304C9.70804 18.3304 9.42117 18.2537 9.16816 18.1079C8.91514 17.9622 8.70485 17.7526 8.55835 17.5"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              {notificationCount > 0 && (
                <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                  {notificationCount > 99 ? '99+' : notificationCount}
                </div>
              )}
            </button>
          )}

          {/* User avatar */}
          {userAvatar && onAvatarClick && (
            <button
              onClick={onAvatarClick}
              className="w-8 h-8 rounded-full overflow-hidden border-2 border-white/20 hover:border-white/40 transition-colors"
            >
              <img
                src={userAvatar}
                alt="User avatar"
                className="w-full h-full object-cover"
              />
            </button>
          )}
        </div>
      }
    />
  );
};

// Status bar with progress indicator
interface ProgressStatusBarProps {
  title: string;
  progress: number; // 0-100
  onBackClick?: () => void;
}

export const ProgressStatusBar: React.FC<ProgressStatusBarProps> = ({
  title,
  progress,
  onBackClick,
}) => {
  return (
    <div className="absolute top-0 left-0 right-0 z-20 bg-black">
      <StatusBar
        title={title}
        showBackButton={!!onBackClick}
        onBackClick={onBackClick}
      />
      
      {/* Progress bar */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/10">
        <div
          className="h-full bg-primary-500 transition-all duration-300"
          style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
        />
      </div>
    </div>
  );
};
