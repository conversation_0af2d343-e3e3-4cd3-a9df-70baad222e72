import { useState, useEffect, useRef, useCallback } from 'react';
import { UserData } from '../services/api';

interface UseRealTimeBalanceProps {
  userData: UserData | null;
  isMining: boolean;
  miningStartTime?: number; // 挖矿开始时间戳（秒）
}

interface RealTimeBalanceResult {
  currentBalance: number;
  miningRate: number; // 每秒挖矿速率
  totalMined: number; // 本次挖矿总产出
  miningDuration: number; // 挖矿持续时间（秒）
}

export const useRealTimeBalance = ({
  userData,
  isMining,
  miningStartTime
}: UseRealTimeBalanceProps): RealTimeBalanceResult => {
  const [currentBalance, setCurrentBalance] = useState(0);
  const [totalMined, setTotalMined] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout>();
  const baseBalanceRef = useRef(0); // 挖矿开始时的基础余额
  const lastUpdateTimeRef = useRef(0); // 上次更新时间
  const maxBalanceRef = useRef(0); // 记录最高余额，防止回退
  const isInitializedRef = useRef(false); // 防止重复初始化
  const lastUserDataRef = useRef<UserData | null>(null); // 记录上次的userData

  // 计算每秒挖矿速率 - 为了演示效果，增加基础速度
  const baseMiningRate = userData ? (userData.userRate * userData.baseSpeed) / 3600 : 0;
  // 为了让数字变化更明显，我们可以增加一个倍数
  const miningRate = baseMiningRate * 10; // 增加10倍，让变化更明显

  // 初始化余额 - 根据online字段和用户状态判断
  useEffect(() => {
    if (!userData) return;

    // 检查是否是新的用户数据
    const isNewUserData = !lastUserDataRef.current ||
                          lastUserDataRef.current.id !== userData.id ||
                          lastUserDataRef.current.userBalance !== userData.userBalance;

    if (isNewUserData) {
      lastUserDataRef.current = userData;

      // 根据online字段判断用户状态
      if (userData.online) {
        // 用户已经在线挖矿，需要计算实时余额
        console.log('用户在线状态，开始计算实时余额', {
          userBalance: userData.userBalance,
          online: userData.online,
          lastSettlementTime: userData.lastSettlementTime
        });

        // 计算从lastSettlementTime到现在的挖矿收益
        const now = Math.floor(Date.now() / 1000);
        const miningDuration = now - userData.lastSettlementTime;
        const miningRate = (userData.userRate * userData.baseSpeed) / 3600 * 10; // 增加10倍演示效果
        const rawCalculatedBalance = userData.userBalance + (miningDuration * miningRate);

        // 修复浮点数精度问题
        const calculatedBalance = Math.round(rawCalculatedBalance * 100) / 100;

        setCurrentBalance(calculatedBalance);
        baseBalanceRef.current = calculatedBalance;
        maxBalanceRef.current = calculatedBalance;
        isInitializedRef.current = true;
      } else {
        // 用户离线状态，直接显示login接口的余额
        console.log('用户离线状态，显示基础余额', {
          userBalance: userData.userBalance,
          online: userData.online
        });

        // 修复浮点数精度问题
        const userBalance = Math.round(userData.userBalance * 100) / 100;
        const currentMax = Math.round(maxBalanceRef.current * 100) / 100;

        // 只有当新余额大于当前余额时才更新，防止回退
        if (userBalance >= currentMax || !isInitializedRef.current) {
          setCurrentBalance(userBalance);
          baseBalanceRef.current = userBalance;
          maxBalanceRef.current = userBalance;
          isInitializedRef.current = true;
        }
      }
    }
  }, [userData]);

  // 计算实时余额的函数
  const calculateRealTimeBalance = useCallback(() => {
    if (!userData || !isMining || !miningStartTime) {
      return;
    }

    const now = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
    const miningDuration = now - miningStartTime; // 挖矿持续时间（秒）

    if (miningDuration < 0) {
      return; // 防止时间异常
    }

    // 计算从挖矿开始到现在的总产出
    const minedAmount = miningDuration * miningRate;

    // 更新余额和挖矿产出 - 每秒增加固定数值
    const incrementPerSecond = miningRate; // 每秒的增量
    const rawNewBalance = baseBalanceRef.current + (miningDuration * incrementPerSecond);

    // 修复浮点数精度问题：四舍五入到2位小数
    const newBalance = Math.round(rawNewBalance * 100) / 100;

    // 确保余额只增不减，使用精确比较（考虑2位小数精度）
    const currentMax = Math.round(maxBalanceRef.current * 100) / 100;

    if (newBalance > currentMax) {
      maxBalanceRef.current = newBalance;
      setCurrentBalance(newBalance);
      console.log('余额更新:', {
        from: currentMax,
        to: newBalance,
        miningDuration,
        incrementPerSecond,
        rawNewBalance
      });
    }

    setTotalMined(minedAmount);
  }, [userData, isMining, miningStartTime, miningRate, currentBalance]);

  // 开始/停止实时更新
  useEffect(() => {
    if (isMining && userData && miningStartTime) {
      console.log('开始实时余额更新', {
        userRate: userData.userRate,
        baseSpeed: userData.baseSpeed,
        miningRate,
        miningStartTime,
        incrementPerSecond: miningRate
      });

      // 清除之前的定时器（如果存在）
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      // 每秒更新一次 - 不立即执行，避免重复动画
      intervalRef.current = setInterval(() => {
        calculateRealTimeBalance();
      }, 1000);
    } else {
      // 停止挖矿时清除定时器
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = undefined;
      }

      // 如果不在挖矿状态，重置挖矿产出但保持当前余额
      if (!isMining) {
        setTotalMined(0);
        // 不要重置currentBalance，保持挖矿后的余额
        // 只有在userData更新时才更新baseBalance
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isMining, userData, miningStartTime, calculateRealTimeBalance]);

  // 当挖矿开始时，记录基础余额
  useEffect(() => {
    if (isMining && miningStartTime && userData) {
      baseBalanceRef.current = userData.userBalance;
      setTotalMined(0);
      console.log('挖矿开始，记录基础余额:', baseBalanceRef.current);
    }
  }, [isMining, miningStartTime, userData]);

  const miningDuration = miningStartTime ? Math.floor(Date.now() / 1000) - miningStartTime : 0;

  return {
    currentBalance,
    miningRate,
    totalMined,
    miningDuration: Math.max(0, miningDuration)
  };
};

// 格式化挖矿速率显示
export const formatMiningRate = (rate: number): string => {
  if (rate >= 1) {
    return `${rate.toFixed(2)}/s`;
  } else if (rate >= 0.01) {
    return `${rate.toFixed(4)}/s`;
  } else {
    return `${(rate * 3600).toFixed(2)}/h`;
  }
};

// 格式化挖矿产出显示
export const formatMinedAmount = (amount: number): string => {
  if (amount >= 1000000) {
    return `+${(amount / 1000000).toFixed(2)}M`;
  } else if (amount >= 1000) {
    return `+${(amount / 1000).toFixed(2)}K`;
  } else if (amount >= 1) {
    return `+${amount.toFixed(2)}`;
  } else {
    return `+${amount.toFixed(4)}`;
  }
};
