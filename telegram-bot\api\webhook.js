// Vercel serverless function for Telegram bot
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const BOT_TOKEN = '8054527269:AAEjKEJ8xQjBHvSfg8dwzJ6wCaGP-DGX7yk';
  const WEBAPP_URL = 'https://tg.tlock.org/';

  // Send message function
  async function sendMessage(chatId, text, options = {}) {
    try {
      const response = await fetch(`https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: chatId,
          text: text,
          parse_mode: 'HTML',
          ...options
        })
      });
      return await response.json();
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }

  const { message, callback_query } = req.body;

  // Handle callback queries (inline button presses)
  if (callback_query) {
    const chatId = callback_query.message.chat.id;
    const data = callback_query.data;

    if (data === 'help') {
      const helpText = `❓ <b>Help & Support</b>

<b>Available Commands:</b>
/start - 🚀 Launch Tlock Mini App
/help - ❓ Get help and support  
/game - 🎮 Game information

<b>How to Play:</b>
1. Tap "Launch Tlock" button
2. Start mining tokens
3. Complete daily tasks
4. Invite friends for bonuses
5. Level up and earn rewards!`;

      await sendMessage(chatId, helpText);
    } else if (data === 'game_info') {
      const gameText = `🎮 <b>About Tlock Game</b>

<b>Features:</b>
• 🪙 Token Mining
• 📈 Level System  
• 🎯 Daily Tasks
• 👥 Friend Referrals
• 🏆 Leaderboards
• 💎 Special Rewards`;

      await sendMessage(chatId, gameText);
    }

    // Answer callback query
    await fetch(`https://api.telegram.org/bot${BOT_TOKEN}/answerCallbackQuery`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        callback_query_id: callback_query.id
      })
    });

    return res.status(200).json({ ok: true });
  }

  // Handle messages
  if (!message) {
    return res.status(200).json({ ok: true });
  }

  const chatId = message.chat.id;
  const text = message.text;
  const firstName = message.from.first_name || 'User';

  console.log(`Received message: ${text} from ${firstName} (${chatId})`);

  try {
    if (text === '/start') {
      const welcomeText = `🚀 <b>TLock is back!</b>

But we're back bigger and better than before.

Welcome to TLock: a new world with its own blockchain, games, apps, and rewards 🚀

Ready to start your adventure, ${firstName}?`;

      const keyboard = {
        inline_keyboard: [
          [
            {
              text: '🎮 Launch Tlock',
              web_app: { url: WEBAPP_URL }
            }
          ],
          [
            {
              text: '📖 Help',
              callback_data: 'help'
            },
            {
              text: '🎯 Game Info',
              callback_data: 'game_info'
            }
          ]
        ]
      };

      await sendMessage(chatId, welcomeText, {
        reply_markup: keyboard
      });

    } else if (text === '/help') {
      const helpText = `❓ <b>Help & Support</b>

<b>Available Commands:</b>
/start - 🚀 Launch Tlock Mini App
/help - ❓ Get help and support  
/game - 🎮 Game information

<b>How to Play:</b>
1. Tap "Launch Tlock" button
2. Start mining tokens
3. Complete daily tasks
4. Invite friends for bonuses
5. Level up and earn rewards!

<b>Need Support?</b>
Contact our support team for assistance.`;

      await sendMessage(chatId, helpText);

    } else if (text === '/game') {
      const gameText = `🎮 <b>About Tlock Game</b>

<b>Features:</b>
• 🪙 Token Mining
• 📈 Level System  
• 🎯 Daily Tasks
• 👥 Friend Referrals
• 🏆 Leaderboards
• 💎 Special Rewards

<b>How to Earn:</b>
• Mine tokens every few hours
• Complete daily missions
• Invite friends to join
• Participate in special events

Ready to play? Tap the button below!`;

      const keyboard = {
        inline_keyboard: [
          [
            {
              text: '🚀 Play Now',
              web_app: { url: WEBAPP_URL }
            }
          ]
        ]
      };

      await sendMessage(chatId, gameText, {
        reply_markup: keyboard
      });

    } else {
      const defaultText = `Hi ${firstName}! 👋

I didn't understand that command. Try:
/start - Launch Tlock
/help - Get help
/game - Game info

Or use the menu button below to play directly!`;

      await sendMessage(chatId, defaultText);
    }

  } catch (error) {
    console.error('Error processing message:', error);
    await sendMessage(chatId, 'Sorry, something went wrong. Please try again later.');
  }

  res.status(200).json({ ok: true });
}
