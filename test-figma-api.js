// 测试 Figma API 连接
const https = require('https');

const FIGMA_API_KEY = '*********************************************';
const TEST_FILE_ID = 'vc4Gyu2XlOtieRgoGXKdmb'; // 从用户提供的链接中提取

console.log('🔍 测试 Figma API 连接...');
console.log('API Key:', FIGMA_API_KEY.substring(0, 10) + '...');
console.log('文件 ID:', TEST_FILE_ID);

const options = {
  hostname: 'api.figma.com',
  port: 443,
  path: `/v1/files/${TEST_FILE_ID}`,
  method: 'GET',
  headers: {
    'X-Figma-Token': FIGMA_API_KEY,
    'Content-Type': 'application/json'
  }
};

const req = https.request(options, (res) => {
  console.log(`\n📡 响应状态: ${res.statusCode}`);
  console.log('响应头:', res.headers);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      
      if (res.statusCode === 200) {
        console.log('\n✅ API 连接成功!');
        console.log('文件名称:', response.name);
        console.log('最后修改:', response.lastModified);
        console.log('版本:', response.version);
        console.log('页面数量:', response.document.children.length);
      } else {
        console.log('\n❌ API 连接失败');
        console.log('错误信息:', response.message || response.err);
      }
    } catch (error) {
      console.log('\n❌ 解析响应失败:', error.message);
      console.log('原始响应:', data.substring(0, 500));
    }
  });
});

req.on('error', (error) => {
  console.log('\n❌ 请求失败:', error.message);
});

req.end();
