import React, { useState, useEffect } from 'react';
import { Routes } from '../types/routes';
import { apiService } from '../services/api';
import type { QuizQuestion, QuizAnswer } from '../services/api';

interface QuizPageProps {
  user?: any;
  onNavigate?: (route: Routes) => void;
}

export const QuizPage: React.FC<QuizPageProps> = ({ user, onNavigate }) => {
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [score, setScore] = useState(0);
  const [totalScore, setTotalScore] = useState(0);
  const [rewardAmount, setRewardAmount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // 加载Quiz题目
  useEffect(() => {
    let isMounted = true; // 防止组件卸载后设置状态

    const loadQuestions = async () => {
      try {
        if (!isMounted) return;
        setLoading(true);
        setError(null);

        console.log('🎯 Loading quiz questions...');
        const questionsData = await apiService.getQuestions();
        console.log('📝 Quiz questions loaded:', questionsData.length, 'questions');

        if (!isMounted) return;
        setQuestions(questionsData);
        // 初始化答案数组
        setSelectedAnswers(new Array(questionsData.length).fill(''));
      } catch (err) {
        console.error('Failed to load quiz questions:', err);
        if (!isMounted) return;
        setError(err instanceof Error ? err.message : 'Failed to load quiz questions');
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // 只有当questions为空时才加载
    if (questions.length === 0) {
      loadQuestions();
    }

    return () => {
      isMounted = false;
    };
  }, []); // 保持空依赖数组，但添加内部检查

  const handleAnswerSelect = (optionKey: string) => {
    const newAnswers = [...selectedAnswers];
    newAnswers[currentQuestion] = optionKey;
    setSelectedAnswers(newAnswers);
  };

  const handleNext = async () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      // 提交答案到API
      await handleFinishQuiz();
    }
  };

  const handleFinishQuiz = async () => {
    try {
      setSubmitting(true);
      setError(null);

      // 构建答案数组
      const answers: QuizAnswer[] = selectedAnswers.map(answer => ({ answer }));

      console.log('🚀 Submitting quiz answers:', answers);

      // 提交答案
      const result = await apiService.submitAnswers(answers);

      console.log('📊 Quiz results received:', result);

      // 设置得分和奖励信息
      setTotalScore(result.totalScore || 0);
      setRewardAmount(result.rewardAmount || 0);
      // 计算正确答案数量（假设每题20分，满分100分）
      setScore(Math.round((result.totalScore || 0) / 20));
      setShowResults(true);
    } catch (err) {
      console.error('Failed to submit quiz answers:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit quiz answers');
    } finally {
      setSubmitting(false);
    }
  };

  const handleRestart = async () => {
    setCurrentQuestion(0);
    setSelectedAnswers([]);
    setShowResults(false);
    setScore(0);
    setTotalScore(0);
    setRewardAmount(0);

    // 重新加载题目
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Restarting quiz, reloading questions...');
      const questionsData = await apiService.getQuestions();
      console.log('📝 Quiz questions reloaded:', questionsData.length, 'questions');

      setQuestions(questionsData);
      // 初始化答案数组
      setSelectedAnswers(new Array(questionsData.length).fill(''));
    } catch (err) {
      console.error('Failed to reload quiz questions:', err);
      setError(err instanceof Error ? err.message : 'Failed to reload quiz questions');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToEarn = () => {
    if (onNavigate) {
      onNavigate(Routes.EARN);
    }
  };

  // 加载状态
  if (loading) {
    return (
      <div className="min-h-screen bg-black text-white flex flex-col items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-lg">Loading Quiz Questions...</p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="min-h-screen bg-black text-white flex flex-col items-center justify-center">
        <div className="text-center px-4">
          <div className="text-6xl mb-4">❌</div>
          <h2 className="text-xl font-bold mb-4">Error Loading Quiz</h2>
          <p className="text-gray-400 mb-8">{error}</p>
          <button
            onClick={handleBackToEarn}
            className="px-6 py-3 bg-blue-500 text-white rounded-2xl font-medium hover:opacity-80 transition-opacity"
          >
            Back to Earn
          </button>
        </div>
      </div>
    );
  }

  // 没有题目
  if (questions.length === 0) {
    return (
      <div className="min-h-screen bg-black text-white flex flex-col items-center justify-center">
        <div className="text-center px-4">
          <div className="text-6xl mb-4">📝</div>
          <h2 className="text-xl font-bold mb-4">No Quiz Available</h2>
          <p className="text-gray-400 mb-8">No quiz questions are available at the moment.</p>
          <button
            onClick={handleBackToEarn}
            className="px-6 py-3 bg-blue-500 text-white rounded-2xl font-medium hover:opacity-80 transition-opacity"
          >
            Back to Earn
          </button>
        </div>
      </div>
    );
  }

  if (showResults) {
    return (
      <div className="min-h-screen bg-black text-white flex flex-col">
        {/* Status Bar */}
        <div className="h-22 flex items-center justify-center" style={{ backgroundColor: '#000000' }}>
          <h1 className="text-xl font-medium">Quiz Results</h1>
        </div>

        {/* Results Content */}
        <div className="flex-1 px-4 py-8 flex flex-col items-center justify-center">
          <div className="w-full max-w-sm text-center">
            <h2 className="text-2xl font-bold mb-6">Quiz Completed!</h2>

            {/* Score Display */}
            <div className="mb-6">
              <p className="text-lg mb-2">Your Score:</p>
              <p className="text-4xl font-bold text-blue-500 mb-2">
                {totalScore}/100
              </p>
              <p className="text-sm text-gray-400">
                ({score}/{questions.length} correct answers)
              </p>
            </div>

            {/* Reward Display */}
            <div className="mb-8 p-4 rounded-2xl" style={{ backgroundColor: '#13171C' }}>
              <p className="text-lg mb-2">Reward Earned:</p>
              <div className="flex items-center justify-center space-x-2">
                <img
                  src="/src/assets/icons/token-icon.png"
                  alt="Token"
                  className="w-6 h-6"
                />
                <p className="text-2xl font-bold text-yellow-400">
                  +{rewardAmount.toLocaleString()}
                </p>
              </div>
            </div>

            {/* Motivational Message */}
            {totalScore >= 80 ? (
              <p className="text-green-400 mb-8">Perfect! You've earned your full rewards!</p>
            ) : totalScore >= 60 ? (
              <p className="text-yellow-400 mb-8">Good job! You've earned partial rewards!</p>
            ) : (
              <p className="text-red-400 mb-8">Keep learning! Try again to earn rewards.</p>
            )}

            <div className="space-y-4">
              <button
                onClick={handleRestart}
                className="w-full py-4 bg-blue-500 text-white rounded-2xl font-medium hover:opacity-80 transition-opacity"
                disabled={submitting}
              >
                {submitting ? 'Loading...' : 'Try Again'}
              </button>

              <button
                onClick={handleBackToEarn}
                className="w-full py-4 text-white rounded-2xl font-medium hover:opacity-80 transition-opacity"
                style={{ backgroundColor: '#13171C' }}
              >
                Back to Earn
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const question = questions[currentQuestion];
  const selectedAnswer = selectedAnswers[currentQuestion];

  return (
    <div className="min-h-screen bg-black text-white flex flex-col">
      {/* Status Bar */}
      <div className="h-22 flex items-center justify-center" style={{ backgroundColor: '#000000' }}>
        <h1 className="text-xl font-medium">Tlock Quiz</h1>
      </div>

      {/* Quiz Content */}
      <div className="flex-1 px-4 py-8">
        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-400">
              Question {currentQuestion + 1} of {questions.length}
            </span>
            <span className="text-sm text-gray-400">
              {Math.round(((currentQuestion + 1) / questions.length) * 100)}%
            </span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${((currentQuestion + 1) / questions.length) * 100}%`
              }}
            />
          </div>
        </div>

        {/* Question */}
        <div className="mb-8">
          <h2 className="text-xl font-medium mb-6">{question.question}</h2>

          {/* Options */}
          <div className="space-y-3">
            {Object.entries(question.options).map(([optionKey, optionText]) => (
              <button
                key={optionKey}
                onClick={() => handleAnswerSelect(optionKey)}
                className={`w-full p-4 rounded-2xl text-left transition-all ${
                  selectedAnswer === optionKey
                    ? 'bg-blue-500 text-white'
                    : 'text-white hover:opacity-80'
                }`}
                style={{
                  backgroundColor: selectedAnswer === optionKey ? '#298AFF' : '#13171C'
                }}
              >
                {optionText}
              </button>
            ))}
          </div>
        </div>

        {/* Next Button */}
        <div className="mt-auto">
          <button
            onClick={handleNext}
            disabled={!selectedAnswer || submitting}
            className={`w-full py-4 rounded-2xl font-medium transition-opacity ${
              selectedAnswer && !submitting
                ? 'bg-blue-500 text-white hover:opacity-80'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            }`}
          >
            {submitting ? 'Submitting...' :
             currentQuestion === questions.length - 1 ? 'Finish Quiz' : 'Next Question'}
          </button>
        </div>
      </div>
    </div>
  );
};
