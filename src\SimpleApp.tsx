import { useState, useEffect } from 'react';
import { Router } from './components/Router';

// Telegram Web App types
declare global {
  interface Window {
    Telegram?: {
      WebApp?: {
        ready: () => void;
        expand: () => void;
        close: () => void;
        MainButton: {
          text: string;
          color: string;
          textColor: string;
          isVisible: boolean;
          isActive: boolean;
          setText: (text: string) => void;
          onClick: (callback: () => void) => void;
          show: () => void;
          hide: () => void;
        };
        themeParams: {
          bg_color?: string;
          text_color?: string;
          hint_color?: string;
          link_color?: string;
          button_color?: string;
          button_text_color?: string;
        };
        initData: string;
        initDataUnsafe: {
          user?: {
            id: number;
            first_name: string;
            last_name?: string;
            username?: string;
            language_code?: string;
          };
        };
      };
    };
  }
}

function SimpleApp() {
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    // Initialize Telegram Web App
    if (window.Telegram?.WebApp) {
      const tg = window.Telegram.WebApp;
      tg.ready();
      tg.expand();

      // Get user data
      if (tg.initDataUnsafe?.user) {
        setUser(tg.initDataUnsafe.user);
      }
    }
  }, []);

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#000000',
      color: 'white',
      padding: '20px'
    }}>
      <h1 style={{ color: '#259AEE', marginBottom: '20px' }}>Tlock App - 简化版</h1>
      <p>应用正在运行...</p>
      {user && (
        <div>
          <p>用户信息:</p>
          <pre style={{ backgroundColor: '#333', padding: '10px', borderRadius: '5px' }}>
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>
      )}
      <Router user={user} />
    </div>
  );
}

export default SimpleApp;
