import React from 'react';
import flag1 from '../assets/figma/flag1.png';
import flag2 from '../assets/figma/flag2.png';
import flag3 from '../assets/figma/flag3.svg';

interface ChatMessageProps {
  id: string;
  username: string;
  level: number;
  levelColor: string;
  message: string;
  timestamp: string;
  avatar?: string;
}

// 等级配置 - 严格按照Figma设计和等级规则
interface LevelConfig {
  badgeColor: string;
  textColor: string;
  messageBoxColor: string;
}

// 等级图标组件 - 直接使用SVG内容
const LevelIcon: React.FC<{ level: number }> = ({ level }) => {
  if (level >= 1 && level <= 10) {
    return (
      <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1.47214 1.82591L3.42861 1H6.40552L8.39389 1.89851L6.63634 2.79702H3.33437L1.47214 1.82591Z" fill="#A2FF5A"/>
        <path d="M1.47196 1.82617L0.200409 3.29247L2.78033 5.05986L3.33419 2.79728L1.47196 1.82617Z" fill="#8ED84E"/>
        <path d="M6.63654 2.79695L7.48704 5.05952L9.7996 3.24146L8.39409 1.89844L6.63654 2.79695Z" fill="#8ED84E"/>
        <path d="M3.33239 2.79736H6.63436L7.48486 5.05993H2.77853L3.33239 2.79736Z" fill="#75BA4D"/>
        <path d="M7.48561 5.05976L7.43312 5.16377L5.13245 8.99992L9.79818 3.2417L7.48561 5.05976Z" fill="#75BA4D"/>
        <path d="M2.78033 5.05962L5.1335 8.99978L0.200409 3.29224L2.78033 5.05962Z" fill="#75BA4D"/>
        <path d="M7.48486 5.06006L5.1317 9.00022L2.77853 5.06006H7.48486Z" fill="#8ED84E"/>
        <path opacity="0.4" d="M1.61682 1.90039C1.72179 2.05538 1.81314 2.22192 1.901 2.37454C2.14168 2.79268 2.29073 3.25556 2.47718 3.69771C2.59573 3.98219 2.67225 4.28311 2.70424 4.59058C2.72105 4.73876 2.73526 4.89759 2.76629 5.04932L2.77963 5.05851L3.33378 2.79594L1.61682 1.90039Z" fill="white"/>
        <path opacity="0.2" d="M7.94095 1.71143C7.91398 1.72387 7.88556 1.73513 7.85598 1.74551C7.52425 1.86019 7.15424 1.89042 6.80858 1.93605C6.39392 1.99088 5.98737 2.03503 5.56835 2.03503C5.07307 2.03503 4.58243 2.06467 4.08744 2.03503C3.59245 2.0054 3.09427 2.02999 2.61001 1.92894C2.32554 1.86967 2.03469 1.81004 1.754 1.72476L1.47214 1.8433L3.33466 2.81441H6.63634L8.39418 1.91531L7.94095 1.71143Z" fill="white"/>
      </svg>
    );
  }

  // 其他等级暂时使用相同的绿色钻石，您可以后续替换为对应的SVG
  return (
    <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1.47214 1.82591L3.42861 1H6.40552L8.39389 1.89851L6.63634 2.79702H3.33437L1.47214 1.82591Z" fill="#A2FF5A"/>
      <path d="M1.47196 1.82617L0.200409 3.29247L2.78033 5.05986L3.33419 2.79728L1.47196 1.82617Z" fill="#8ED84E"/>
      <path d="M6.63654 2.79695L7.48704 5.05952L9.7996 3.24146L8.39409 1.89844L6.63654 2.79695Z" fill="#8ED84E"/>
      <path d="M3.33239 2.79736H6.63436L7.48486 5.05993H2.77853L3.33239 2.79736Z" fill="#75BA4D"/>
      <path d="M7.48561 5.05976L7.43312 5.16377L5.13245 8.99992L9.79818 3.2417L7.48561 5.05976Z" fill="#75BA4D"/>
      <path d="M2.78033 5.05962L5.1335 8.99978L0.200409 3.29224L2.78033 5.05962Z" fill="#75BA4D"/>
      <path d="M7.48486 5.06006L5.1317 9.00022L2.77853 5.06006H7.48486Z" fill="#8ED84E"/>
      <path opacity="0.4" d="M1.61682 1.90039C1.72179 2.05538 1.81314 2.22192 1.901 2.37454C2.14168 2.79268 2.29073 3.25556 2.47718 3.69771C2.59573 3.98219 2.67225 4.28311 2.70424 4.59058C2.72105 4.73876 2.73526 4.89759 2.76629 5.04932L2.77963 5.05851L3.33378 2.79594L1.61682 1.90039Z" fill="white"/>
      <path opacity="0.2" d="M7.94095 1.71143C7.91398 1.72387 7.88556 1.73513 7.85598 1.74551C7.52425 1.86019 7.15424 1.89042 6.80858 1.93605C6.39392 1.99088 5.98737 2.03503 5.56835 2.03503C5.07307 2.03503 4.58243 2.06467 4.08744 2.03503C3.59245 2.0054 3.09427 2.02999 2.61001 1.92894C2.32554 1.86967 2.03469 1.81004 1.754 1.72476L1.47214 1.8433L3.33466 2.81441H6.63634L8.39418 1.91531L7.94095 1.71143Z" fill="white"/>
    </svg>
  );
};

const getLevelConfig = (level: number): LevelConfig => {
  if (level >= 1 && level <= 10) {
    return {
      badgeColor: 'rgba(86, 161, 255, 0.2)', // 蓝色背景
      textColor: '#56A1FF', // 蓝色文字
      messageBoxColor: '#2A3441' // 蓝色调消息框
    };
  } else if (level >= 11 && level <= 20) {
    return {
      badgeColor: 'rgba(86, 161, 255, 0.2)', // 蓝色背景 (Figma中显示为蓝色)
      textColor: '#56A1FF', // 蓝色文字
      messageBoxColor: '#2A3441' // 蓝色调消息框
    };
  } else if (level >= 21 && level <= 30) {
    return {
      badgeColor: 'rgba(148, 113, 255, 0.2)', // 紫色背景
      textColor: '#9471FF', // 紫色文字
      messageBoxColor: '#3A2D4A' // 紫色调消息框
    };
  } else if (level >= 31 && level <= 40) {
    return {
      badgeColor: 'rgba(255, 219, 82, 0.2)', // 黄色背景
      textColor: '#FFDB52', // 黄色文字
      messageBoxColor: '#4A4229' // 黄色调消息框
    };
  } else if (level >= 41 && level <= 50) {
    return {
      badgeColor: 'rgba(255, 86, 161, 0.2)', // 红色背景
      textColor: '#FF56A1', // 红色文字
      messageBoxColor: '#4A2A3A' // 红色调消息框
    };
  }

  // 默认配置
  return {
    badgeColor: 'rgba(86, 161, 255, 0.2)',
    textColor: '#56A1FF',
    messageBoxColor: '#2A3441'
  };
};

// 根据用户名生成固定的国旗，确保同一用户的国旗不会变化
const getUserFlag = (username: string): string => {
  const flags = [flag1, flag2, flag3];
  // 使用用户名的哈希值来确定国旗，确保同一用户总是显示相同的国旗
  let hash = 0;
  for (let i = 0; i < username.length; i++) {
    const char = username.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  const index = Math.abs(hash) % flags.length;
  return flags[index] || '🌍';
};

export const ChatMessage: React.FC<ChatMessageProps> = ({
  username,
  level,
  levelColor, // 这个参数现在被忽略，使用内部计算的配置
  message,
  timestamp,
  avatar
}) => {
  const levelConfig = getLevelConfig(level);
  const flagIcon = getUserFlag(username); // 使用基于用户名的固定国旗

  return (
    <div className="mb-4">
      {/* User Info Row - 严格按照Figma设计 */}
      <div className="flex items-center space-x-2 mb-2">
        {/* Level Badge - 包含图标和等级数字 */}
        <div
          className="rounded-lg flex items-center justify-center px-2 py-1 gap-1"
          style={{
            backgroundColor: levelConfig.badgeColor,
            borderRadius: '8px',
            height: '20px'
          }}
        >
          {/* Level Icon - 真实的Figma设计图标 */}
          <div className="w-3 h-3 flex items-center justify-center">
            <LevelIcon level={level} />
          </div>

          {/* Level Number */}
          <span
            className="font-bold text-xs leading-none"
            style={{
              color: levelConfig.textColor,
              fontFamily: 'PingFang SC',
              fontSize: '10px'
            }}
          >
            {level}
          </span>
        </div>

        {/* Country Flag - 12x12 圆形 */}
        <div className="w-3 h-3 rounded-full flex-shrink-0 overflow-hidden">
          <img
            src={flagIcon}
            alt="Country Flag"
            className="w-full h-full object-cover"
          />
        </div>

        {/* Username and Time */}
        <span
          className="text-xs text-gray-300 font-medium"
          style={{ fontFamily: 'PingFang SC' }}
        >
          {username}
        </span>
        <span
          className="text-xs text-gray-500"
          style={{ fontFamily: 'PingFang SC' }}
        >
          {timestamp}
        </span>
      </div>

      {/* Message Bubble - 根据等级使用不同颜色 */}
      <div
        className="text-sm px-3 py-2 rounded-2xl inline-block max-w-[280px] break-words ml-7"
        style={{
          backgroundColor: levelConfig.messageBoxColor,
          borderRadius: '16px'
        }}
      >
        <span
          className="text-white leading-relaxed"
          style={{ fontFamily: 'PingFang SC' }}
        >
          {message}
        </span>
      </div>
    </div>
  );
};
