import React, { useState, useEffect, useRef } from 'react';

interface FlipClockDigitProps {
  digit: string;
  previousDigit: string;
  isAnimating: boolean;
  duration: number;
  fontSize?: string;
}

// 翻页时钟样式的数字组件
const FlipClockDigit: React.FC<FlipClockDigitProps> = ({
  digit,
  previousDigit,
  isAnimating,
  duration,
  fontSize = '32px'
}) => {
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (isAnimating && digit !== previousDigit && /\d/.test(digit) && /\d/.test(previousDigit)) {
      setIsTransitioning(true);

      // 动画结束后重置
      const timer = setTimeout(() => {
        setIsTransitioning(false);
      }, duration);

      return () => clearTimeout(timer);
    } else if (!isAnimating) {
      // 如果父组件不再动画，立即重置状态
      setIsTransitioning(false);
    }
  }, [digit, previousDigit, isAnimating, duration]);

  // 如果不是数字，直接显示
  if (!/\d/.test(digit)) {
    return (
      <span
        style={{
          fontSize,
          lineHeight: fontSize,
          display: 'inline-block',
          textAlign: 'center',
          minWidth: '0.5ch'
        }}
      >
        {digit}
      </span>
    );
  }

  const currentNum = parseInt(digit) || 0;
  const prevNum = parseInt(previousDigit) || 0;
  const isIncreasing = currentNum > prevNum;

  return (
    <div
      className="relative inline-block overflow-hidden"
      style={{
        height: fontSize,
        lineHeight: fontSize,
        fontSize,
        width: '1ch',
        textAlign: 'center'
      }}
    >
      {/* 旧数字 */}
      <div
        className="absolute inset-0 flex items-center justify-center"
        style={{
          transform: isTransitioning
            ? `translateY(${isIncreasing ? '-100%' : '100%'})`
            : 'translateY(0%)',
          transition: `transform ${duration}ms cubic-bezier(0.23, 1, 0.32, 1)`,
          opacity: isTransitioning ? 0 : 1
        }}
      >
        {previousDigit}
      </div>

      {/* 新数字 */}
      <div
        className="absolute inset-0 flex items-center justify-center"
        style={{
          transform: isTransitioning
            ? 'translateY(0%)'
            : `translateY(${isIncreasing ? '100%' : '-100%'})`,
          transition: `transform ${duration}ms cubic-bezier(0.23, 1, 0.32, 1)`,
          opacity: isTransitioning ? 1 : 0
        }}
      >
        {digit}
      </div>
    </div>
  );
};

interface FlipClockNumberProps {
  value: number;
  className?: string;
  style?: React.CSSProperties;
  formatNumber?: (num: number) => string;
  duration?: number;
}

// 默认格式化函数
const defaultFormatNumber = (num: number): string => {
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

export const FlipClockNumber: React.FC<FlipClockNumberProps> = ({
  value,
  className = '',
  style = {},
  formatNumber = defaultFormatNumber,
  duration = 800
}) => {
  const [previousValue, setPreviousValue] = useState(value);
  const [isAnimating, setIsAnimating] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const lastValueRef = useRef(value);

  const currentFormatted = formatNumber(value);
  const previousFormatted = formatNumber(previousValue);

  useEffect(() => {
    // 只有当值真正改变时才触发动画
    if (value !== lastValueRef.current && value !== previousValue) {
      console.log('FlipClockNumber: 值变化', {
        from: previousValue,
        to: value,
        lastValue: lastValueRef.current,
        formatted: { from: previousFormatted, to: currentFormatted }
      });

      lastValueRef.current = value;
      setIsAnimating(true);

      // 清除之前的定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // 动画结束后更新previousValue
      timeoutRef.current = setTimeout(() => {
        setPreviousValue(value);
        setIsAnimating(false);
        console.log('FlipClockNumber: 动画完成', { value });
      }, duration);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, previousValue, duration, previousFormatted, currentFormatted]);

  // 将格式化后的字符串转换为字符数组
  const currentChars = currentFormatted.split('');
  const previousChars = previousFormatted.split('');

  // 确保两个数组长度一致
  const maxLength = Math.max(currentChars.length, previousChars.length);
  while (currentChars.length < maxLength) {
    currentChars.unshift(' ');
  }
  while (previousChars.length < maxLength) {
    previousChars.unshift(' ');
  }

  const fontSize = style.fontSize || '32px';

  return (
    <div 
      className={className} 
      style={{ 
        ...style, 
        display: 'inline-flex', 
        alignItems: 'center',
        fontFamily: style.fontFamily || 'HarmonyOS Sans SC, sans-serif',
        fontWeight: style.fontWeight || 'bold'
      }}
    >
      {currentChars.map((char, index) => {
        const prevChar = previousChars[index] || ' ';
        
        return (
          <FlipClockDigit
            key={index}
            digit={char}
            previousDigit={prevChar}
            isAnimating={isAnimating}
            duration={duration}
            fontSize={fontSize}
          />
        );
      })}
    </div>
  );
};

// TOK余额专用组件
interface TOKBalanceProps {
  balance: number;
  className?: string;
  style?: React.CSSProperties;
}

export const FlipClockTOKBalance: React.FC<TOKBalanceProps> = ({
  balance,
  className = '',
  style = {}
}) => {
  const formatTOKBalance = (num: number): string => {
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  return (
    <FlipClockNumber
      value={balance}
      className={className}
      style={style}
      formatNumber={formatTOKBalance}
      duration={500} // 减少动画时间，配合1秒跳动
    />
  );
};
