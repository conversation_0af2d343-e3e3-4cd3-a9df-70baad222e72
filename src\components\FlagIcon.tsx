import React from 'react';

interface FlagIconProps {
  countryCode: string;
  size?: number;
  className?: string;
}

export const FlagIcon: React.FC<FlagIconProps> = ({ 
  countryCode, 
  size = 22, 
  className = '' 
}) => {
  const getFlagSvg = (code: string) => {
    switch (code.toUpperCase()) {
      case 'US':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="#B22234"/>
            <rect width="24" height="1.38" y="1.38" fill="white"/>
            <rect width="24" height="1.38" y="4.15" fill="white"/>
            <rect width="24" height="1.38" y="6.92" fill="white"/>
            <rect width="24" height="1.38" y="9.69" fill="white"/>
            <rect width="24" height="1.38" y="12.46" fill="white"/>
            <rect width="24" height="1.38" y="15.23" fill="white"/>
            <rect width="9.6" height="9.69" fill="#3C3B6E"/>
          </svg>
        );
      
      case 'CN':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="#DE2910"/>
            <polygon points="3,3 4.5,6 1.5,6" fill="#FFDE00"/>
            <polygon points="7,2 7.5,3.5 6.5,3.5" fill="#FFDE00"/>
            <polygon points="7,5 7.5,6.5 6.5,6.5" fill="#FFDE00"/>
            <polygon points="7,8 7.5,9.5 6.5,9.5" fill="#FFDE00"/>
            <polygon points="7,11 7.5,12.5 6.5,12.5" fill="#FFDE00"/>
          </svg>
        );
      
      case 'JP':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="white"/>
            <circle cx="12" cy="9" r="5.4" fill="#BC002D"/>
          </svg>
        );
      
      case 'KR':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="white"/>
            <circle cx="12" cy="9" r="3.6" fill="none" stroke="#CD2E3A" strokeWidth="0.8"/>
            <path d="M12,5.4 A3.6,3.6 0 0,1 12,12.6 A1.8,1.8 0 0,0 12,9 A1.8,1.8 0 0,1 12,5.4 Z" fill="#0047A0"/>
            <path d="M12,12.6 A3.6,3.6 0 0,1 12,5.4 A1.8,1.8 0 0,0 12,9 A1.8,1.8 0 0,1 12,12.6 Z" fill="#CD2E3A"/>
          </svg>
        );
      
      case 'GB':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="#012169"/>
            <path d="M0,0 L24,18 M24,0 L0,18" stroke="white" strokeWidth="2"/>
            <path d="M0,0 L24,18 M24,0 L0,18" stroke="#C8102E" strokeWidth="1.2"/>
            <path d="M12,0 L12,18 M0,9 L24,9" stroke="white" strokeWidth="3"/>
            <path d="M12,0 L12,18 M0,9 L24,9" stroke="#C8102E" strokeWidth="2"/>
          </svg>
        );
      
      case 'DE':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="6" fill="black"/>
            <rect width="24" height="6" y="6" fill="#DD0000"/>
            <rect width="24" height="6" y="12" fill="#FFCE00"/>
          </svg>
        );
      
      case 'FR':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="8" height="18" fill="#0055A4"/>
            <rect width="8" height="18" x="8" fill="white"/>
            <rect width="8" height="18" x="16" fill="#EF4135"/>
          </svg>
        );
      
      case 'IT':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="8" height="18" fill="#009246"/>
            <rect width="8" height="18" x="8" fill="white"/>
            <rect width="8" height="18" x="16" fill="#CE2B37"/>
          </svg>
        );
      
      case 'ES':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="#AA151B"/>
            <rect width="24" height="9" y="4.5" fill="#F1BF00"/>
          </svg>
        );
      
      case 'RU':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="6" fill="white"/>
            <rect width="24" height="6" y="6" fill="#0039A6"/>
            <rect width="24" height="6" y="12" fill="#D52B1E"/>
          </svg>
        );
      
      case 'CA':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="white"/>
            <rect width="6" height="18" fill="#FF0000"/>
            <rect width="6" height="18" x="18" fill="#FF0000"/>
            <path d="M12,6 L13,8 L15,8 L13.5,9.5 L14,11.5 L12,10.5 L10,11.5 L10.5,9.5 L9,8 L11,8 Z" fill="#FF0000"/>
          </svg>
        );
      
      case 'AU':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="#012169"/>
            <rect width="12" height="9" fill="#012169"/>
            <path d="M0,0 L12,9 M12,0 L0,9" stroke="white" strokeWidth="1"/>
            <path d="M0,0 L12,9 M12,0 L0,9" stroke="#C8102E" strokeWidth="0.6"/>
            <path d="M6,0 L6,9 M0,4.5 L12,4.5" stroke="white" strokeWidth="1.5"/>
            <path d="M6,0 L6,9 M0,4.5 L12,4.5" stroke="#C8102E" strokeWidth="1"/>
          </svg>
        );
      
      case 'BR':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="#009739"/>
            <path d="M12,2 L22,9 L12,16 L2,9 Z" fill="#FEDD00"/>
            <circle cx="12" cy="9" r="3.5" fill="#012169"/>
          </svg>
        );
      
      case 'IN':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="6" fill="#FF9933"/>
            <rect width="24" height="6" y="6" fill="white"/>
            <rect width="24" height="6" y="12" fill="#138808"/>
            <circle cx="12" cy="9" r="2" fill="none" stroke="#000080" strokeWidth="0.5"/>
          </svg>
        );
      
      case 'MX':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="8" height="18" fill="#006847"/>
            <rect width="8" height="18" x="8" fill="white"/>
            <rect width="8" height="18" x="16" fill="#CE1126"/>
          </svg>
        );
      
      case 'EE':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="6" fill="#0072CE"/>
            <rect width="24" height="6" y="6" fill="black"/>
            <rect width="24" height="6" y="12" fill="white"/>
          </svg>
        );

      case 'FI':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="white"/>
            <rect width="24" height="3" y="7.5" fill="#003580"/>
            <rect width="3" height="18" x="7" fill="#003580"/>
          </svg>
        );

      case 'SE':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="#006AA7"/>
            <rect width="24" height="2" y="8" fill="#FECC00"/>
            <rect width="2" height="18" x="7" fill="#FECC00"/>
          </svg>
        );

      case 'NO':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="#EF2B2D"/>
            <rect width="24" height="2" y="8" fill="white"/>
            <rect width="2" height="18" x="7" fill="white"/>
            <rect width="24" height="1" y="8.5" fill="#002868"/>
            <rect width="1" height="18" x="7.5" fill="#002868"/>
          </svg>
        );

      case 'DK':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="#C60C30"/>
            <rect width="24" height="2" y="8" fill="white"/>
            <rect width="2" height="18" x="7" fill="white"/>
          </svg>
        );

      case 'NL':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="6" fill="#AE1C28"/>
            <rect width="24" height="6" y="6" fill="white"/>
            <rect width="24" height="6" y="12" fill="#21468B"/>
          </svg>
        );

      case 'BE':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="8" height="18" fill="black"/>
            <rect width="8" height="18" x="8" fill="#FDDA24"/>
            <rect width="8" height="18" x="16" fill="#EF3340"/>
          </svg>
        );

      case 'CH':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="#FF0000"/>
            <rect width="6" height="2" x="9" y="8" fill="white"/>
            <rect width="2" height="6" x="11" y="6" fill="white"/>
          </svg>
        );

      case 'AT':
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="6" fill="#ED2939"/>
            <rect width="24" height="6" y="6" fill="white"/>
            <rect width="24" height="6" y="12" fill="#ED2939"/>
          </svg>
        );

      default:
        // 默认显示一个通用的国旗图标
        return (
          <svg width={size} height={size} viewBox="0 0 24 18" className={className}>
            <rect width="24" height="18" fill="#E5E5E5" stroke="#999" strokeWidth="1"/>
            <text x="12" y="11" textAnchor="middle" fontSize="8" fill="#666">{countryCode}</text>
          </svg>
        );
    }
  };

  return getFlagSvg(countryCode);
};
