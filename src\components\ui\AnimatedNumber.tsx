import React, { useState, useEffect, useRef } from 'react';

interface AnimatedNumberProps {
  value: number;
  className?: string;
  style?: React.CSSProperties;
  formatNumber?: (num: number) => string;
  duration?: number; // 动画持续时间（毫秒）
}

interface DigitProps {
  digit: string;
  previousDigit: string;
  isAnimating: boolean;
  duration: number;
  fontSize?: string;
}

// 单个数字滚动组件 - 类似翻页时钟效果
const AnimatedDigit: React.FC<DigitProps> = ({
  digit,
  previousDigit,
  isAnimating,
  duration,
  fontSize = '32px'
}) => {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isAnimating && digit !== previousDigit && /\d/.test(digit) && /\d/.test(previousDigit)) {
      const currentNum = parseInt(digit) || 0;
      const prevNum = parseInt(previousDigit) || 0;

      if (currentNum !== prevNum) {
        setIsTransitioning(true);

        // 动画结束后重置状态
        setTimeout(() => {
          setIsTransitioning(false);
        }, duration);
      }
    }
  }, [digit, previousDigit, isAnimating, duration]);

  // 如果不是数字，直接显示
  if (!/\d/.test(digit)) {
    return <span style={{ fontSize }}>{digit}</span>;
  }

  const currentNum = parseInt(digit) || 0;
  const prevNum = parseInt(previousDigit) || 0;

  // 判断滚动方向
  const isScrollingUp = currentNum > prevNum;

  return (
    <div
      ref={containerRef}
      className="relative inline-block overflow-hidden"
      style={{
        height: fontSize,
        lineHeight: fontSize,
        fontSize,
        width: '1ch' // 确保每个数字占用固定宽度
      }}
    >
      {/* 当前数字 */}
      <div
        className="absolute inset-0 flex items-center justify-center transition-transform"
        style={{
          transform: isTransitioning
            ? (isScrollingUp ? 'translateY(-100%)' : 'translateY(100%)')
            : 'translateY(0%)',
          transitionDuration: `${duration}ms`,
          transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)', // 更平滑的缓动
        }}
      >
        {previousDigit}
      </div>

      {/* 新数字 */}
      <div
        className="absolute inset-0 flex items-center justify-center transition-transform"
        style={{
          transform: isTransitioning
            ? 'translateY(0%)'
            : (isScrollingUp ? 'translateY(100%)' : 'translateY(-100%)'),
          transitionDuration: `${duration}ms`,
          transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)', // 更平滑的缓动
        }}
      >
        {digit}
      </div>
    </div>
  );
};

// 默认数字格式化函数 - 修复精度问题
const defaultFormatNumber = (num: number): string => {
  // 先四舍五入到2位小数，避免浮点数精度问题
  const rounded = Math.round(num * 100) / 100;
  return rounded.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

export const AnimatedNumber: React.FC<AnimatedNumberProps> = ({
  value,
  className = '',
  style = {},
  formatNumber = defaultFormatNumber,
  duration = 400
}) => {
  const [previousValue, setPreviousValue] = useState(value);
  const [isAnimating, setIsAnimating] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const currentFormatted = formatNumber(value);
  const previousFormatted = formatNumber(previousValue);

  useEffect(() => {
    if (value !== previousValue) {
      setIsAnimating(true);

      // 清除之前的定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // 动画结束后更新previousValue
      timeoutRef.current = setTimeout(() => {
        setPreviousValue(value);
        setIsAnimating(false);
      }, duration);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, previousValue, duration]);

  // 将格式化后的字符串转换为字符数组
  const currentChars = currentFormatted.split('');
  const previousChars = previousFormatted.split('');

  // 确保两个数组长度一致（用空格填充较短的）
  const maxLength = Math.max(currentChars.length, previousChars.length);
  while (currentChars.length < maxLength) {
    currentChars.unshift(' ');
  }
  while (previousChars.length < maxLength) {
    previousChars.unshift(' ');
  }

  // 从style中提取fontSize
  const fontSize = style.fontSize || '32px';

  return (
    <div className={className} style={{ ...style, display: 'inline-flex', alignItems: 'baseline' }}>
      {currentChars.map((char, index) => {
        const prevChar = previousChars[index] || ' ';

        // 使用动画组件处理所有字符
        return (
          <AnimatedDigit
            key={index}
            digit={char}
            previousDigit={prevChar}
            isAnimating={isAnimating}
            duration={duration}
            fontSize={fontSize}
          />
        );
      })}
    </div>
  );
};

// 专门用于TOK余额显示的组件
interface TOKBalanceProps {
  balance: number;
  className?: string;
  style?: React.CSSProperties;
}

export const TOKBalance: React.FC<TOKBalanceProps> = ({
  balance,
  className = '',
  style = {}
}) => {
  const formatTOKBalance = (num: number): string => {
    // 先四舍五入到2位小数，避免浮点数精度问题
    const rounded = Math.round(num * 100) / 100;
    return rounded.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  return (
    <AnimatedNumber
      value={balance}
      className={className}
      style={style}
      formatNumber={formatTOKBalance}
      duration={600} // 稍微慢一点的动画，更平滑
    />
  );
};
