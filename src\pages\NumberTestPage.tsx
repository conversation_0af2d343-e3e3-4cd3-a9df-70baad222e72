import React, { useState, useEffect } from 'react';
import { SmoothScrollTOKBalance } from '../components/ui/SmoothScrollNumber';
import { SimpleFlipTOKBalance } from '../components/ui/SimpleFlipNumber';

export const NumberTestPage: React.FC = () => {
  const [balance1, setBalance1] = useState(1234.56);
  const [balance2, setBalance2] = useState(9876.54);

  // 自动测试数字变化
  useEffect(() => {
    const interval = setInterval(() => {
      // 随机增加或减少余额
      setBalance1(prev => {
        const change = (Math.random() - 0.5) * 100; // -50 到 +50 的随机变化
        const newValue = Math.max(0, prev + change);
        return Math.round(newValue * 100) / 100; // 保持2位小数
      });

      setBalance2(prev => {
        const change = (Math.random() - 0.5) * 200; // -100 到 +100 的随机变化
        const newValue = Math.max(0, prev + change);
        return Math.round(newValue * 100) / 100; // 保持2位小数
      });
    }, 2000); // 每2秒变化一次

    return () => clearInterval(interval);
  }, []);

  const testValues = [
    0.00,
    1.23,
    12.34,
    123.45,
    1234.56,
    9999.99,
    10000.00,
    99999.99
  ];

  return (
    <div className="min-h-screen bg-black text-white p-6">
      <div className="max-w-md mx-auto space-y-8">
        <h1 className="text-2xl font-bold text-center mb-8">数字翻转测试</h1>

        {/* 自动变化测试 */}
        <div className="space-y-6">
          <h2 className="text-lg font-semibold">自动变化测试</h2>
          
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-sm text-gray-400 mb-2">SmoothScrollTOKBalance</h3>
            <SmoothScrollTOKBalance
              balance={balance1}
              className="text-white font-bold"
              style={{
                fontFamily: 'HarmonyOS Sans SC',
                fontSize: '32px',
                lineHeight: '1em'
              }}
            />
            <p className="text-xs text-gray-500 mt-2">当前值: {balance1}</p>
          </div>

          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-sm text-gray-400 mb-2">SimpleFlipTOKBalance</h3>
            <SimpleFlipTOKBalance
              balance={balance2}
              className="text-white font-bold"
              style={{
                fontFamily: 'HarmonyOS Sans SC',
                fontSize: '32px',
                lineHeight: '1em'
              }}
            />
            <p className="text-xs text-gray-500 mt-2">当前值: {balance2}</p>
          </div>
        </div>

        {/* 手动测试按钮 */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">手动测试</h2>
          
          <div className="grid grid-cols-2 gap-2">
            {testValues.map((value, index) => (
              <button
                key={index}
                onClick={() => {
                  setBalance1(value);
                  setBalance2(value);
                }}
                className="bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded text-sm"
              >
                {value.toFixed(2)}
              </button>
            ))}
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => {
                setBalance1(prev => prev + 0.01);
                setBalance2(prev => prev + 0.01);
              }}
              className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded text-sm flex-1"
            >
              +0.01
            </button>
            <button
              onClick={() => {
                setBalance1(prev => Math.max(0, prev - 0.01));
                setBalance2(prev => Math.max(0, prev - 0.01));
              }}
              className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded text-sm flex-1"
            >
              -0.01
            </button>
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => {
                setBalance1(prev => prev + 1);
                setBalance2(prev => prev + 1);
              }}
              className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded text-sm flex-1"
            >
              +1.00
            </button>
            <button
              onClick={() => {
                setBalance1(prev => Math.max(0, prev - 1));
                setBalance2(prev => Math.max(0, prev - 1));
              }}
              className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded text-sm flex-1"
            >
              -1.00
            </button>
          </div>
        </div>

        {/* 特殊测试案例 */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">特殊测试案例</h2>

          <div className="grid grid-cols-1 gap-2">
            <button
              onClick={() => {
                // 测试小数点后数字从9变到0的情况
                setBalance1(1234.59);
                setBalance2(1234.59);
                setTimeout(() => {
                  setBalance1(1234.60);
                  setBalance2(1234.60);
                }, 100);
              }}
              className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded text-sm"
            >
              测试 .59 → .60 (小数进位)
            </button>

            <button
              onClick={() => {
                // 测试小数点后数字从0变到9的情况
                setBalance1(1234.60);
                setBalance2(1234.60);
                setTimeout(() => {
                  setBalance1(1234.59);
                  setBalance2(1234.59);
                }, 100);
              }}
              className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded text-sm"
            >
              测试 .60 → .59 (小数退位)
            </button>

            <button
              onClick={() => {
                // 测试整数位进位
                setBalance1(999.99);
                setBalance2(999.99);
                setTimeout(() => {
                  setBalance1(1000.00);
                  setBalance2(1000.00);
                }, 100);
              }}
              className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded text-sm"
            >
              测试 999.99 → 1000.00 (整数进位)
            </button>

            <button
              onClick={() => {
                // 测试小数点后单个数字变化
                setBalance1(100.01);
                setBalance2(100.01);
                setTimeout(() => {
                  setBalance1(100.02);
                  setBalance2(100.02);
                }, 100);
              }}
              className="bg-orange-600 hover:bg-orange-700 px-4 py-2 rounded text-sm"
            >
              测试 .01 → .02 (小数位单个数字)
            </button>

            <button
              onClick={() => {
                // 测试小数点后数字从8变到9
                setBalance1(100.08);
                setBalance2(100.08);
                setTimeout(() => {
                  setBalance1(100.09);
                  setBalance2(100.09);
                }, 100);
              }}
              className="bg-orange-600 hover:bg-orange-700 px-4 py-2 rounded text-sm"
            >
              测试 .08 → .09 (小数位边界)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
