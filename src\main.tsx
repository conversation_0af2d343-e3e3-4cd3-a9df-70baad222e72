import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
// import TestApp from './TestApp.tsx';
// import SimpleApp from './SimpleApp.tsx';
import './styles/index.css';

// Initialize Telegram Web App
if (window.Telegram?.WebApp) {
  const tg = window.Telegram.WebApp;
  tg.ready();
  tg.expand();
  
  // Set theme colors
  if (tg.themeParams) {
    const root = document.documentElement;
    Object.entries(tg.themeParams).forEach(([key, value]) => {
      root.style.setProperty(`--tg-theme-${key.replace(/_/g, '-')}`, value);
    });
    root.style.setProperty('--tg-color-scheme', tg.colorScheme);
  }
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  // 临时禁用StrictMode以避免开发环境的双重API调用
  // <React.StrictMode>
    <App />
  // </React.StrictMode>,
);
