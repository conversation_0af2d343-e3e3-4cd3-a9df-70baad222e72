/**
 * Route definitions for the Tlock app
 */

export enum Routes {
  // Main pages (按照Figma设计的4个底部导航)
  STARTUP = '/',
  MINING = '/mining',      // 挖矿页面 (主页)
  DOCUMENTS = '/documents', // 文档页面
  EARN = '/earn',          // 赚取页面
  FRIENDS = '/friends',    // 好友页面

  // Sub pages
  SETTINGS = '/settings',
  PROFILE = '/profile',
  DAILY_REWARDS = '/earn/daily',
  READ_TO_EARN = '/documents/read',
  QUIZ = '/quiz',
  INVITE = '/invite',
  WALLET = '/wallet',
  TELEGRAM_TEST = '/telegram-test',
  URL_PARAMS_TEST = '/url-params-test',
  MEDIA_GENERATOR = '/media-generator',
  BOT_TEST = '/bot-test',
  QUIZ_TEST = '/quiz-test',
  NUMBER_ANIMATION_TEST = '/number-animation-test',

  // Modal/Overlay pages
  CHAT_ROOM = '/chat',
  FRIEND_DETAIL = '/friend/:id',
  REWARD_DETAIL = '/reward/:id',
}

export interface RouteConfig {
  path: string;
  title: string;
  showBackButton?: boolean;
  showBottomNav?: boolean;
}

export const routeConfigs: Record<Routes, RouteConfig> = {
  [Routes.STARTUP]: {
    path: '/',
    title: 'Tlock',
    showBackButton: false,
    showBottomNav: false,
  },
  [Routes.MINING]: {
    path: '/mining',
    title: 'Tlock',
    showBackButton: false,
    showBottomNav: true,
  },
  [Routes.DOCUMENTS]: {
    path: '/documents',
    title: 'Tlock',
    showBackButton: false,
    showBottomNav: true,
  },
  [Routes.EARN]: {
    path: '/earn',
    title: 'Tlock',
    showBackButton: false,
    showBottomNav: true,
  },
  [Routes.FRIENDS]: {
    path: '/friends',
    title: 'Tlock',
    showBackButton: false,
    showBottomNav: true,
  },
  [Routes.SETTINGS]: {
    path: '/settings',
    title: 'Tlock',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.PROFILE]: {
    path: '/profile',
    title: 'Tlock',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.DAILY_REWARDS]: {
    path: '/earn/daily',
    title: 'Tlock',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.READ_TO_EARN]: {
    path: '/documents/read',
    title: 'Tlock',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.QUIZ]: {
    path: '/quiz',
    title: 'Tlock',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.INVITE]: {
    path: '/invite',
    title: 'Tlock',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.WALLET]: {
    path: '/wallet',
    title: 'Tlock',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.CHAT_ROOM]: {
    path: '/chat',
    title: 'Tlock',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.FRIEND_DETAIL]: {
    path: '/friend/:id',
    title: 'Tlock',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.REWARD_DETAIL]: {
    path: '/reward/:id',
    title: 'Tlock',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.TELEGRAM_TEST]: {
    path: '/telegram-test',
    title: 'Telegram Test',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.URL_PARAMS_TEST]: {
    path: '/url-params-test',
    title: 'URL Params Test',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.MEDIA_GENERATOR]: {
    path: '/media-generator',
    title: 'Media Generator',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.BOT_TEST]: {
    path: '/bot-test',
    title: 'Bot Test',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.QUIZ_TEST]: {
    path: '/quiz-test',
    title: 'Quiz Test',
    showBackButton: true,
    showBottomNav: false,
  },
  [Routes.NUMBER_ANIMATION_TEST]: {
    path: '/number-animation-test',
    title: 'Number Animation Test',
    showBackButton: true,
    showBottomNav: false,
  },
};
