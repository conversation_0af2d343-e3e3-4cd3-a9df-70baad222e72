# Figma Context MCP 集成指南

## 📖 项目简介

Figma Context MCP 是一个专为 AI 编程工具设计的 Model Context Protocol 服务器，能够让 AI 助手直接访问和理解 Figma 设计文件。

### 🎯 主要优势

- **精确设计实现**：AI 可以直接读取设计规范，而不是依赖截图
- **一键生成代码**：从 Figma 设计直接生成对应的前端代码
- **智能数据处理**：自动简化和优化设计数据，提高 AI 理解准确性
- **多框架支持**：支持 React、Vue、Angular 等多种前端框架

## 🚀 快速开始

### 1. 环境要求

- Node.js >= 18.0.0
- 有效的 Figma API Key
- 支持 MCP 的 AI 工具（如 Cursor）

### 2. 配置步骤

#### 方式一：直接使用 (推荐)

```bash
npx -y figma-developer-mcp --figma-api-key=你的API密钥 --stdio
```

#### 方式二：配置文件方式

将以下配置添加到你的 MCP 配置文件中：

```json
{
  "mcpServers": {
    "Figma Context MCP": {
      "command": "cmd",
      "args": ["/c", "npx", "-y", "figma-developer-mcp", "--figma-api-key=你的API密钥", "--stdio"]
    }
  }
}
```

### 3. 验证安装

运行测试脚本：
```bash
test-figma-mcp.bat
```

## 💡 使用方法

### 基本用法

1. **获取 Figma 链接**
   - 打开你的 Figma 设计文件
   - 复制文件、框架或组件的链接

2. **在 AI 工具中使用**
   ```
   请分析这个 Figma 设计并生成对应的 React 组件：
   https://www.figma.com/design/vc4Gyu2XlOtieRgoGXKdmb/Tlock?node-id=460-465
   ```

3. **AI 自动处理**
   - AI 会自动获取设计数据
   - 分析布局、样式、组件结构
   - 生成对应的代码实现

### 高级用法

- **指定框架**：要求生成特定框架的代码
- **样式系统**：指定使用 CSS Modules、Styled Components 等
- **响应式设计**：要求生成响应式布局代码
- **组件拆分**：要求将复杂设计拆分为多个组件

## 🔧 故障排除

### 常见问题

1. **端口占用错误**
   ```bash
   # 查找占用端口的进程
   netstat -ano | findstr :3333
   # 终止进程
   taskkill /PID 进程ID /F
   ```

2. **API Key 无效**
   - 检查 Figma API Key 是否正确
   - 确认 API Key 有访问相应文件的权限

3. **网络连接问题**
   - 确保网络可以访问 Figma API
   - 检查防火墙设置

### 调试模式

启用调试模式获取更多信息：
```bash
npx -y figma-developer-mcp --figma-api-key=你的API密钥 --stdio --debug
```

## 📚 更多资源

- [官方文档](https://www.framelink.ai/docs)
- [GitHub 仓库](https://github.com/GLips/Figma-Context-MCP)
- [示例项目](https://github.com/GLips/Figma-Context-MCP/tree/main/docs)

## 🤝 支持

如果遇到问题，可以：
- 查看 GitHub Issues
- 加入 Discord 社区
- 查阅官方文档
