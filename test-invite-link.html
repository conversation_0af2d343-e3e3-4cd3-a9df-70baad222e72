<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试邀请链接生成</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            word-break: break-all;
        }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 邀请链接生成测试</h1>
        
        <div class="test-section">
            <h3>1. 设置测试邀请码</h3>
            <input type="text" id="inviteCode" value="TESTCODE123" placeholder="输入邀请码">
            <button onclick="setInviteCode()">设置邀请码</button>
            <div id="setCodeResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 生成邀请链接</h3>
            <button onclick="generateInviteLink()">生成邀请链接</button>
            <div id="generateResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 测试分享功能</h3>
            <button onclick="testShareFunction()">测试分享</button>
            <div id="shareResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 验证链接格式</h3>
            <div id="formatCheck" class="result">
                <p><strong>期望格式:</strong> https://t.me/HabbyBabyBot?startapp=ref_INVITECODE</p>
                <p><strong>错误格式:</strong> https://t.me/HabbyBabyBot/app?startapp=INVITECODE</p>
            </div>
        </div>
    </div>

    <script>
        // 模拟 InviteService 的核心功能
        class MockInviteService {
            static invitationCode = null;
            
            static setInvitationCode(code) {
                this.invitationCode = code;
                if (typeof localStorage !== 'undefined') {
                    localStorage.setItem('tlock_invitation_code', code);
                }
            }
            
            static getInvitationCode() {
                if (!this.invitationCode && typeof localStorage !== 'undefined') {
                    this.invitationCode = localStorage.getItem('tlock_invitation_code');
                }
                return this.invitationCode;
            }
            
            static async getBotUsername() {
                // 模拟异步获取机器人用户名
                return 'HabbyBabyBot';
            }
            
            static async generateInviteLink() {
                const code = this.getInvitationCode();
                if (!code) {
                    throw new Error('邀请码未设置，请先调用setInvitationCode');
                }
                
                const botUsername = await this.getBotUsername();
                // 使用正确的邀请链接格式，邀请码前加 ref_ 前缀
                return `https://t.me/${botUsername}?startapp=ref_${code}`;
            }
            
            static async shareInviteLink(text) {
                const inviteLink = await this.generateInviteLink();
                if (!inviteLink) {
                    throw new Error('无法生成邀请链接，邀请码未设置');
                }
                
                const shareText = text || 'Join TLock and unlock exclusive features! 🔐✨';
                const fullMessage = `${shareText}\n${inviteLink}`;
                
                // 模拟分享功能
                return {
                    shareText,
                    inviteLink,
                    fullMessage,
                    method: 'openTelegramLink'
                };
            }
        }

        function setInviteCode() {
            const code = document.getElementById('inviteCode').value;
            const resultDiv = document.getElementById('setCodeResult');
            
            try {
                MockInviteService.setInvitationCode(code);
                resultDiv.innerHTML = `<div class="success">✅ 邀请码设置成功: ${code}</div>`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 设置失败: ${error.message}</div>`;
                resultDiv.className = 'result error';
            }
        }

        async function generateInviteLink() {
            const resultDiv = document.getElementById('generateResult');
            
            try {
                const link = await MockInviteService.generateInviteLink();
                const isCorrectFormat = link.includes('?startapp=ref_');
                const formatStatus = isCorrectFormat ? '✅ 格式正确' : '❌ 格式错误';
                
                resultDiv.innerHTML = `
                    <div class="${isCorrectFormat ? 'success' : 'error'}">
                        <p><strong>生成的链接:</strong> ${link}</p>
                        <p><strong>格式检查:</strong> ${formatStatus}</p>
                        <p><strong>包含 ref_ 前缀:</strong> ${link.includes('ref_') ? '✅ 是' : '❌ 否'}</p>
                        <p><strong>使用正确格式:</strong> ${link.includes('?startapp=') ? '✅ 是' : '❌ 否'}</p>
                    </div>
                `;
                resultDiv.className = `result ${isCorrectFormat ? 'success' : 'error'}`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 生成失败: ${error.message}</div>`;
                resultDiv.className = 'result error';
            }
        }

        async function testShareFunction() {
            const resultDiv = document.getElementById('shareResult');
            
            try {
                const shareData = await MockInviteService.shareInviteLink(
                    '🚀 Join me on Tlock and start mining TLOCK tokens together!\n\n💰 Get bonus tokens when you join with my link\n⚡️ Start mining immediately\n🎁 Earn rewards for both of us\n\n#Tlock #Mining #Crypto'
                );
                
                const isCorrectFormat = shareData.inviteLink.includes('?startapp=ref_');
                
                resultDiv.innerHTML = `
                    <div class="${isCorrectFormat ? 'success' : 'error'}">
                        <p><strong>分享方法:</strong> ${shareData.method}</p>
                        <p><strong>邀请链接:</strong> ${shareData.inviteLink}</p>
                        <p><strong>格式检查:</strong> ${isCorrectFormat ? '✅ 正确' : '❌ 错误'}</p>
                        <p><strong>完整消息:</strong></p>
                        <pre style="white-space: pre-wrap; background: #f8f9fa; padding: 10px; border-radius: 4px;">${shareData.fullMessage}</pre>
                    </div>
                `;
                resultDiv.className = `result ${isCorrectFormat ? 'success' : 'error'}`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 分享测试失败: ${error.message}</div>`;
                resultDiv.className = 'result error';
            }
        }

        // 页面加载时自动设置测试邀请码
        window.onload = function() {
            setInviteCode();
        };
    </script>
</body>
</html>
