// Telegram Web App utilities
declare global {
  interface Window {
    Telegram?: {
      WebApp: {
        ready: () => void;
        expand: () => void;
        close: () => void;
        initData: string;
        initDataUnsafe: {
          query_id?: string;
          user?: {
            id: number;
            first_name: string;
            last_name?: string;
            username?: string;
            language_code?: string;
            is_premium?: boolean;
            photo_url?: string;
          };
          auth_date: number;
          hash: string;
          start_param?: string;
        };
        version: string;
        platform: string;
        colorScheme: 'light' | 'dark';
        themeParams: {
          bg_color?: string;
          text_color?: string;
          hint_color?: string;
          link_color?: string;
          button_color?: string;
          button_text_color?: string;
          secondary_bg_color?: string;
        };
        isExpanded: boolean;
        viewportHeight: number;
        viewportStableHeight: number;
        headerColor: string;
        backgroundColor: string;
        isClosingConfirmationEnabled: boolean;
        BackButton: {
          isVisible: boolean;
          show: () => void;
          hide: () => void;
          onClick: (callback: () => void) => void;
          offClick: (callback: () => void) => void;
        };
        MainButton: {
          text: string;
          color: string;
          textColor: string;
          isVisible: boolean;
          isActive: boolean;
          isProgressVisible: boolean;
          setText: (text: string) => void;
          onClick: (callback: () => void) => void;
          offClick: (callback: () => void) => void;
          show: () => void;
          hide: () => void;
          enable: () => void;
          disable: () => void;
          showProgress: (leaveActive?: boolean) => void;
          hideProgress: () => void;
        };
        HapticFeedback: {
          impactOccurred: (style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft') => void;
          notificationOccurred: (type: 'error' | 'success' | 'warning') => void;
          selectionChanged: () => void;
        };
        onEvent: (eventType: string, eventHandler: () => void) => void;
        offEvent: (eventType: string, eventHandler: () => void) => void;
        sendData: (data: string) => void;
        openLink: (url: string, options?: { try_instant_view?: boolean }) => void;
        openTelegramLink: (url: string) => void;
        showPopup: (params: {
          title?: string;
          message: string;
          buttons?: Array<{
            id?: string;
            type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive';
            text: string;
          }>;
        }, callback?: (buttonId: string) => void) => void;
        showAlert: (message: string, callback?: () => void) => void;
        showConfirm: (message: string, callback?: (confirmed: boolean) => void) => void;
        setHeaderColor: (color: string) => void;
        setBackgroundColor: (color: string) => void;
        enableClosingConfirmation: () => void;
        disableClosingConfirmation: () => void;
      };
    };
  }
}

export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  photo_url?: string;
}

export interface TelegramInitData {
  query_id?: string;
  user?: TelegramUser;
  auth_date: number;
  hash: string;
  start_param?: string;
}

class TelegramWebApp {
  private tg: typeof window.Telegram.WebApp | null = null;

  constructor() {
    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      this.tg = window.Telegram.WebApp;
      this.init();
    }
  }

  private init() {
    if (!this.tg) return;
    
    // Initialize the app
    this.tg.ready();
    this.tg.expand();
    
    // Set theme colors
    this.updateTheme();
    
    // Listen for theme changes
    this.tg.onEvent('themeChanged', () => {
      this.updateTheme();
    });
  }

  private updateTheme() {
    if (!this.tg) return;
    
    const root = document.documentElement;
    const themeParams = this.tg.themeParams;
    
    if (themeParams) {
      Object.entries(themeParams).forEach(([key, value]) => {
        if (value) {
          root.style.setProperty(`--tg-theme-${key.replace(/_/g, '-')}`, value);
        }
      });
    }
    
    root.style.setProperty('--tg-color-scheme', this.tg.colorScheme);
  }

  // Check if running in Telegram
  public isInTelegram(): boolean {
    return !!this.tg;
  }

  // Get user data
  public getUser(): TelegramUser | null {
    return this.tg?.initDataUnsafe?.user || null;
  }

  // Get init data
  public getInitData(): TelegramInitData | null {
    return this.tg?.initDataUnsafe || null;
  }

  // Get raw init data for validation
  public getRawInitData(): string {
    return this.tg?.initData || '';
  }

  // Get start parameter
  public getStartParam(): string | null {
    return this.tg?.initDataUnsafe?.start_param || null;
  }

  // Theme utilities
  public getColorScheme(): 'light' | 'dark' {
    return this.tg?.colorScheme || 'dark';
  }

  public getThemeParams() {
    return this.tg?.themeParams || {};
  }

  // UI Controls
  public showMainButton(text: string, callback: () => void) {
    if (!this.tg?.MainButton) return;
    
    this.tg.MainButton.setText(text);
    this.tg.MainButton.onClick(callback);
    this.tg.MainButton.show();
  }

  public hideMainButton() {
    if (!this.tg?.MainButton) return;
    this.tg.MainButton.hide();
  }

  public showBackButton(callback: () => void) {
    if (!this.tg?.BackButton) return;
    
    this.tg.BackButton.onClick(callback);
    this.tg.BackButton.show();
  }

  public hideBackButton() {
    if (!this.tg?.BackButton) return;
    this.tg.BackButton.hide();
  }

  // Haptic feedback
  public hapticFeedback(type: 'impact' | 'notification' | 'selection', style?: string) {
    if (!this.tg?.HapticFeedback) return;
    
    switch (type) {
      case 'impact':
        this.tg.HapticFeedback.impactOccurred(style as any || 'medium');
        break;
      case 'notification':
        this.tg.HapticFeedback.notificationOccurred(style as any || 'success');
        break;
      case 'selection':
        this.tg.HapticFeedback.selectionChanged();
        break;
    }
  }

  // Popups and alerts
  public showAlert(message: string): Promise<void> {
    return new Promise((resolve) => {
      if (!this.tg) {
        alert(message);
        resolve();
        return;
      }
      
      this.tg.showAlert(message, () => resolve());
    });
  }

  public showConfirm(message: string): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.tg) {
        resolve(confirm(message));
        return;
      }
      
      this.tg.showConfirm(message, (confirmed) => resolve(confirmed));
    });
  }

  // Navigation
  public openLink(url: string, tryInstantView = false) {
    if (!this.tg) {
      window.open(url, '_blank');
      return;
    }
    
    this.tg.openLink(url, { try_instant_view: tryInstantView });
  }

  public openTelegramLink(url: string) {
    if (!this.tg) {
      window.open(url, '_blank');
      return;
    }
    
    this.tg.openTelegramLink(url);
  }

  // Close the app
  public close() {
    if (!this.tg) return;
    this.tg.close();
  }

  // Send data back to bot (for keyboard button mini apps)
  public sendData(data: string) {
    if (!this.tg) return;
    this.tg.sendData(data);
  }

  // Enable/disable closing confirmation
  public enableClosingConfirmation() {
    if (!this.tg) return;
    this.tg.enableClosingConfirmation();
  }

  public disableClosingConfirmation() {
    if (!this.tg) return;
    this.tg.disableClosingConfirmation();
  }
}

// Create singleton instance
export const telegramWebApp = new TelegramWebApp();

// Export utility functions
export const isTelegramWebApp = () => telegramWebApp.isInTelegram();
export const getTelegramUser = () => telegramWebApp.getUser();
export const getTelegramInitData = () => telegramWebApp.getInitData();
export const getTelegramStartParam = () => telegramWebApp.getStartParam();
