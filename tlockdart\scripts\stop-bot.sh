#!/bin/bash

# Tlock Bot 停止脚本

# 自动检测脚本所在目录的父目录作为 BOT_DIR
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BOT_DIR="$(dirname "$SCRIPT_DIR")"
PID_FILE="$BOT_DIR/bot.pid"

if [ ! -f "$PID_FILE" ]; then
    echo "❌ PID文件不存在，Bot可能没有运行"
    exit 1
fi

PID=$(cat "$PID_FILE")

if ! ps -p $PID > /dev/null 2>&1; then
    echo "❌ Bot进程不存在 (PID: $PID)"
    rm -f "$PID_FILE"
    exit 1
fi

echo "🛑 停止 Tlock Bot (PID: $PID)..."

# 尝试优雅停止
kill $PID

# 等待进程结束
for i in {1..10}; do
    if ! ps -p $PID > /dev/null 2>&1; then
        echo "✅ Bot 已停止"
        rm -f "$PID_FILE"
        exit 0
    fi
    echo "⏳ 等待进程结束... ($i/10)"
    sleep 1
done

# 强制停止
echo "⚠️ 优雅停止失败，强制终止..."
kill -9 $PID

if ! ps -p $PID > /dev/null 2>&1; then
    echo "✅ Bot 已强制停止"
    rm -f "$PID_FILE"
else
    echo "❌ 无法停止 Bot 进程"
    exit 1
fi
