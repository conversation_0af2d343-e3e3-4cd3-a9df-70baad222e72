<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="./tlock-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    
    <!-- Telegram Web App Script - Must be loaded first -->
    <script src="https://telegram.org/js/telegram-web-app.js?58"></script>
    
    <!-- Meta tags for Telegram Mini App -->
    <meta name="telegram-web-app" content="true" />
    <meta name="theme-color" content="#259AEE" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Tlock" />
    
    <!-- PWA Meta Tags -->
    <link rel="manifest" href="./manifest.json" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="application-name" content="Tlock" />

    <!-- iOS Specific Meta Tags -->
    <link rel="apple-touch-icon" sizes="180x180" href="./apple-touch-icon.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="./apple-touch-icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="./apple-touch-icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="./apple-touch-icon-120x120.png" />

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="./favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="./favicon-16x16.png" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://tlock.xyz/" />
    <meta property="og:title" content="Tlock - Decentralized Social App" />
    <meta property="og:description" content="Join the decentralized social platform with mining mechanics and daily rewards" />
    <meta property="og:image" content="/og-image.png" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://tlock.xyz/" />
    <meta property="twitter:title" content="Tlock - Decentralized Social App" />
    <meta property="twitter:description" content="Join the decentralized social platform with mining mechanics and daily rewards" />
    <meta property="twitter:image" content="/twitter-image.png" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://api.tlock.xyz" />
    <link rel="dns-prefetch" href="https://api.tlock.xyz" />
    
    <title>Tlock - Decentralized Social App</title>
    <meta name="description" content="Join Tlock, the decentralized social platform built on Telegram with mining mechanics, daily rewards, and social features." />
    
    <!-- CSS Variables for Telegram Theme -->
    <style>
      :root {
        --tg-theme-bg-color: #171717;
        --tg-theme-text-color: #ffffff;
        --tg-theme-hint-color: #a1a1aa;
        --tg-theme-link-color: #259aee;
        --tg-theme-button-color: #259aee;
        --tg-theme-button-text-color: #ffffff;
        --tg-theme-secondary-bg-color: #202428;
        --tg-viewport-height: 100vh;
        --tg-viewport-stable-height: 100vh;
        --tg-color-scheme: dark;
      }
      
      /* Prevent zoom on iOS */
      input, select, textarea {
        font-size: 16px !important;
      }
      
      /* Smooth scrolling */
      html {
        scroll-behavior: smooth;
      }
      
      /* Remove default margins and paddings */
      * {
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        padding: 0;
        font-family: 'HarmonyOS Sans SC', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background-color: var(--tg-theme-bg-color);
        color: var(--tg-theme-text-color);
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        -webkit-tap-highlight-color: transparent;
      }
      
      /* Loading screen - 临时禁用 */
      #loading-screen {
        display: none !important;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        animation: pulse 2s ease-in-out infinite;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.05); opacity: 0.8; }
      }
      
      /* Hide scrollbar but keep functionality */
      ::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">
        <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect width="40" height="40" rx="8" fill="white" fill-opacity="0.9"/>
          <path d="M20 8L28 14V26L20 32L12 26V14L20 8Z" fill="#259AEE"/>
        </svg>
      </div>
    </div>
    
    <!-- React App Root -->
    <div id="root"></div>
    
    <!-- Initialize Telegram Web App -->
    <script>
      // Initialize Telegram Web App as early as possible
      if (window.Telegram && window.Telegram.WebApp) {
        const tg = window.Telegram.WebApp;
        tg.ready();
        tg.expand();
        
        // Set theme colors based on Telegram theme
        if (tg.themeParams) {
          const root = document.documentElement;
          Object.entries(tg.themeParams).forEach(([key, value]) => {
            root.style.setProperty(`--tg-theme-${key.replace(/_/g, '-')}`, value);
          });
          root.style.setProperty('--tg-color-scheme', tg.colorScheme);
        }
        
        // Update viewport height
        const updateViewportHeight = () => {
          document.documentElement.style.setProperty('--tg-viewport-height', `${tg.viewportHeight}px`);
          document.documentElement.style.setProperty('--tg-viewport-stable-height', `${tg.viewportStableHeight}px`);
        };
        
        updateViewportHeight();
        tg.onEvent('viewportChanged', updateViewportHeight);
        
        // Handle theme changes
        tg.onEvent('themeChanged', () => {
          if (tg.themeParams) {
            const root = document.documentElement;
            Object.entries(tg.themeParams).forEach(([key, value]) => {
              root.style.setProperty(`--tg-theme-${key.replace(/_/g, '-')}`, value);
            });
            root.style.setProperty('--tg-color-scheme', tg.colorScheme);
          }
        });
      }
      
      // Hide loading screen after a short delay
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.classList.add('fade-out');
            setTimeout(() => {
              loadingScreen.remove();
            }, 300);
          }
        }, 500);
      });
    </script>
    
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
