/**
 * Quiz API Test
 * 测试Quiz相关的API功能
 */

import { apiService } from '../services/api';
import type { QuizQuestion, QuizAnswer } from '../services/api';

// Mock data for testing
const mockQuestions: QuizQuestion[] = [
  {
    question: "What characteristic best describes Tlock's system?",
    options: {
      A: "Centralized control with a single point of failure",
      B: "Unstoppable resilience in extreme conditions",
      C: "Limited interaction and scalability",
      D: "Exclusive permissioned access"
    }
  },
  {
    question: "What component is crucial for organizing posts within Tlock?",
    options: {
      A: "Data compression",
      B: "Indexing and Sorting",
      C: "Image processing",
      D: "Advertisement algorithms"
    }
  }
];

const mockAnswers: QuizAnswer[] = [
  { answer: "B" },
  { answer: "B" }
];

/**
 * 测试获取Quiz题目
 */
export async function testGetQuestions(): Promise<void> {
  try {
    console.log('Testing getQuestions API...');
    
    const questions = await apiService.getQuestions();
    
    console.log('✅ getQuestions API test passed');
    console.log('Questions received:', questions.length);
    console.log('Sample question:', questions[0]?.question);
    
    // 验证数据结构
    if (questions.length > 0) {
      const firstQuestion = questions[0];
      if (!firstQuestion.question || !firstQuestion.options) {
        throw new Error('Invalid question structure');
      }
      
      const optionKeys = Object.keys(firstQuestion.options);
      if (!optionKeys.includes('A') || !optionKeys.includes('B')) {
        throw new Error('Invalid options structure');
      }
    }
    
    console.log('✅ Question structure validation passed');
    
  } catch (error) {
    console.error('❌ getQuestions API test failed:', error);
    throw error;
  }
}

/**
 * 测试提交Quiz答案
 */
export async function testSubmitAnswers(): Promise<void> {
  try {
    console.log('Testing submitAnswers API...');
    
    // 首先获取题目
    const questions = await apiService.getQuestions();
    
    if (questions.length === 0) {
      console.log('⚠️ No questions available, skipping submit test');
      return;
    }
    
    // 构建答案（选择第一个选项）
    const answers: QuizAnswer[] = questions.map(() => ({ answer: "A" }));
    
    const result = await apiService.submitAnswers(answers);
    
    console.log('✅ submitAnswers API test passed');
    console.log('Result:', result);
    
    // 验证返回数据结构
    if (typeof result.score === 'undefined' && typeof result.correctAnswers === 'undefined') {
      console.log('⚠️ No score information in result');
    }
    
  } catch (error) {
    console.error('❌ submitAnswers API test failed:', error);
    throw error;
  }
}

/**
 * 运行所有Quiz测试
 */
export async function runQuizTests(): Promise<void> {
  console.log('🚀 Starting Quiz API tests...');
  
  try {
    await testGetQuestions();
    await testSubmitAnswers();
    
    console.log('🎉 All Quiz API tests passed!');
  } catch (error) {
    console.error('💥 Quiz API tests failed:', error);
    throw error;
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数暴露到全局
  (window as any).runQuizTests = runQuizTests;
  (window as any).testGetQuestions = testGetQuestions;
  (window as any).testSubmitAnswers = testSubmitAnswers;
}
