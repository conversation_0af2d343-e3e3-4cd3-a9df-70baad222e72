import React, { useState, useEffect } from 'react';

interface MiningCountdownProps {
  isActive: boolean;
  onComplete: () => void;
  duration: number; // in seconds
  showOnlyTime?: boolean; // Only show time, no status text
}

export const MiningCountdown: React.FC<MiningCountdownProps> = ({
  isActive,
  onComplete,
  duration,
  showOnlyTime = false
}) => {
  const [timeLeft, setTimeLeft] = useState(duration);

  // 当duration改变时，重新设置timeLeft
  useEffect(() => {
    setTimeLeft(duration);
  }, [duration]);

  useEffect(() => {
    if (!isActive) {
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          onComplete();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isActive, onComplete]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isActive) return null;

  // If showOnlyTime is true, return just the time string
  if (showOnlyTime) {
    return <span>{formatTime(timeLeft)}</span>;
  }

  return (
    <div className="text-center py-4 px-4 mx-4">
      {/* Status Text with Arrow */}
      <div className="flex items-center justify-center text-sm text-gray-400 mb-2">
        <span className="mr-2">▲</span>
        <span>In the free mining process...</span>
      </div>

      {/* Countdown Timer */}
      <div className="text-3xl font-bold text-white">
        {formatTime(timeLeft)}
      </div>
    </div>
  );
};
