import React, { useState, useEffect, useRef } from 'react';

// 滚动数字组件 - 实现真正的时钟滚动效果
interface ScrollingDigitProps {
  fromDigit: number;
  toDigit: number;
  duration: number;
  fontSize: string;
}

const ScrollingDigit: React.FC<ScrollingDigitProps> = ({
  fromDigit,
  toDigit,
  duration,
  fontSize
}) => {
  const [progress, setProgress] = useState(0);
  const animationRef = useRef<number>();
  const startTimeRef = useRef<number>();

  useEffect(() => {
    const animate = (timestamp: number) => {
      if (!startTimeRef.current) {
        startTimeRef.current = timestamp;
      }

      const elapsed = timestamp - startTimeRef.current;
      const newProgress = Math.min(elapsed / duration, 1);

      setProgress(newProgress);

      if (newProgress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [duration]);

  // 计算当前应该显示的数字
  const getCurrentDigit = () => {
    if (progress === 1) return toDigit;

    // 使用缓动函数
    const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3);
    const easedProgress = easeOutCubic(progress);

    // 计算数字差值
    let diff = toDigit - fromDigit;

    // 处理跨越0的情况（如9->0）
    if (Math.abs(diff) > 5) {
      if (diff > 0) {
        diff = diff - 10; // 9->0 变成 9->(-1)
      } else {
        diff = diff + 10; // 0->9 变成 0->19
      }
    }

    const currentValue = fromDigit + (diff * easedProgress);
    let displayDigit = Math.round(currentValue) % 10;
    if (displayDigit < 0) displayDigit += 10;

    return displayDigit;
  };

  // 计算滚动位置
  const getScrollPosition = () => {
    if (progress === 1) return 0;

    const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3);
    const easedProgress = easeOutCubic(progress);

    // 根据数字变化方向决定滚动方向
    const isIncreasing = (toDigit > fromDigit && Math.abs(toDigit - fromDigit) <= 5) ||
                        (fromDigit > toDigit && Math.abs(toDigit - fromDigit) > 5);

    const scrollPercent = easedProgress * 100;
    return isIncreasing ? -scrollPercent : scrollPercent;
  };

  const currentDigit = getCurrentDigit();
  const scrollPosition = getScrollPosition();

  return (
    <div className="absolute inset-0 flex items-center justify-center">
      <div
        style={{
          transform: `translateY(${scrollPosition}%)`,
          transition: progress === 1 ? 'none' : undefined
        }}
      >
        {currentDigit}
      </div>
    </div>
  );
};

interface SimpleFlipNumberProps {
  value: number;
  className?: string;
  style?: React.CSSProperties;
  formatNumber?: (num: number) => string;
  duration?: number;
}

// 默认格式化函数
const defaultFormatNumber = (num: number): string => {
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

export const SimpleFlipNumber: React.FC<SimpleFlipNumberProps> = ({
  value,
  className = '',
  style = {},
  formatNumber = defaultFormatNumber,
  duration = 500
}) => {
  const [displayValue, setDisplayValue] = useState(value);
  const [isAnimating, setIsAnimating] = useState(false);
  const animationRef = useRef<NodeJS.Timeout>();
  const lastUpdateRef = useRef(value);

  useEffect(() => {
    // 防止重复动画：只有当值真正改变且不在动画中时才更新
    if (value !== lastUpdateRef.current && !isAnimating) {
      console.log('SimpleFlipNumber: 开始动画', { from: displayValue, to: value });
      
      lastUpdateRef.current = value;
      setIsAnimating(true);

      // 清除之前的定时器
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }

      // 延迟更新显示值，让动画有时间执行
      animationRef.current = setTimeout(() => {
        setDisplayValue(value);
        
        // 动画结束
        setTimeout(() => {
          setIsAnimating(false);
          console.log('SimpleFlipNumber: 动画结束', { value });
        }, duration);
      }, 50); // 很短的延迟，让动画开始
    }

    return () => {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    };
  }, [value, displayValue, isAnimating, duration]);

  const currentFormatted = formatNumber(displayValue);
  const targetFormatted = formatNumber(value);

  // 将字符串分解为字符数组
  const currentChars = currentFormatted.split('');
  const targetChars = targetFormatted.split('');

  // 确保长度一致
  const maxLength = Math.max(currentChars.length, targetChars.length);
  while (currentChars.length < maxLength) {
    currentChars.unshift(' ');
  }
  while (targetChars.length < maxLength) {
    targetChars.unshift(' ');
  }

  const fontSize = style.fontSize || '32px';

  return (
    <div 
      className={className} 
      style={{ 
        ...style, 
        display: 'inline-flex', 
        alignItems: 'center'
      }}
    >
      {currentChars.map((char, index) => {
        const targetChar = targetChars[index] || ' ';
        const shouldAnimate = isAnimating && char !== targetChar && /\d/.test(char) && /\d/.test(targetChar);

        // 如果不是数字，直接显示
        if (!/\d/.test(char)) {
          return (
            <span key={index} style={{ fontSize, minWidth: '0.5ch', textAlign: 'center' }}>
              {char}
            </span>
          );
        }

        return (
          <div
            key={index}
            className="relative inline-block overflow-hidden"
            style={{
              height: fontSize,
              lineHeight: fontSize,
              fontSize,
              width: '1ch',
              textAlign: 'center'
            }}
          >
            {shouldAnimate ? (
              <ScrollingDigit
                fromDigit={parseInt(char)}
                toDigit={parseInt(targetChar)}
                duration={duration}
                fontSize={fontSize}
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                {char}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

// TOK余额专用组件
interface TOKBalanceProps {
  balance: number;
  className?: string;
  style?: React.CSSProperties;
}

export const SimpleFlipTOKBalance: React.FC<TOKBalanceProps> = ({
  balance,
  className = '',
  style = {}
}) => {
  const formatTOKBalance = (num: number): string => {
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  return (
    <SimpleFlipNumber
      value={balance}
      className={className}
      style={style}
      formatNumber={formatTOKBalance}
      duration={800} // 增加动画时长，让滚动效果更明显
    />
  );
};
